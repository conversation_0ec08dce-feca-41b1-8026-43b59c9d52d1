<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CartController;
use App\Http\Controllers\Api\ContactController;
use App\Http\Controllers\Api\ContactSupportController;
use App\Http\Controllers\Api\FixedPriceController;
use App\Http\Controllers\Api\FrontendAppController;
use App\Http\Controllers\Api\Jne\BranchController as JneBranchController;
use App\Http\Controllers\Api\Jne\DestinationController as JneDestinationController;
use App\Http\Controllers\Api\Jne\JneExpeditionController;
use App\Http\Controllers\Api\Jne\OriginController as JneOriginController;
use App\Http\Controllers\Api\JubelioController;
use App\Http\Controllers\Api\JubelioShipmentController;
use App\Http\Controllers\Api\MembershipLevelController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\ProductDiscountController;
use App\Http\Controllers\Api\Settings\NotificationChannelController;
use App\Http\Controllers\Api\Settings\NotificationTypeController;
use App\Http\Controllers\Api\Settings\UserNotificationController;
use App\Http\Controllers\Api\SenderAddressController;
use App\Http\Controllers\Api\ShippingAddressController;
use App\Http\Controllers\Api\StatusController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\WebhookController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Route::get('/user', function (Request $request) {
//     return $request->user();
// })->middleware('auth:sanctum');


// Public routes
Route::get('/', function (Request $request) {
    return response()->json([
        'message' => 'Wellcome to Otoapi.'
    ]);
});

Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/email/resend', [AuthController::class, 'resendPublicVerificationEmail']);
Route::post('/webhook/xendit/invoice', [WebhookController::class, 'handleXenditInvoiceWebhook']);
Route::post('/webhook/jubelio/invoice', [WebhookController::class, 'handleJubelioInvoiceWebhook']);
Route::get('/status/{identifier}', [StatusController::class, 'check'])->name('status.check');
Route::get('/public/support-contacts', [ContactSupportController::class, 'publicIndex'])->name('public.support-contacts.index');
// Route::post('/check-activation', [AuthController::class, 'checkActivationStatus']);

// Password Reset Routes
Route::post('/forgot-password', [AuthController::class, 'forgotPassword'])->name('password.email');
Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('password.update');

// Email Verification Routes
Route::get('/email/verify/{id}/{hash}', [AuthController::class, 'verifyEmail'])
    ->middleware(['signed'])->name('verification.verify');

Route::post('/email/verification-notification', [AuthController::class, 'resendVerificationEmail'])
    ->middleware(['auth:sanctum', 'throttle:6,1'])->name('verification.send');

// Protected routes
Route::middleware(['auth:sanctum', 'maintenance'])->group(function () {

    // User management routes (admin only)
    Route::middleware('admin')->group(function () {
        Route::apiResource('users', UserController::class);
        Route::get('users-resellers', [UserController::class, 'getResellers']);
        Route::apiResource('payments', PaymentController::class)->only(['index', 'show']);
        Route::apiResource('frontend-apps', FrontendAppController::class);
        Route::apiResource('contacts', ContactController::class);
        Route::apiResource('contact-supports', ContactSupportController::class);
        Route::apiResource('membership-levels', MembershipLevelController::class);

        Route::apiResource('jne-branches', JneBranchController::class);
        Route::apiResource('jne-origins', JneOriginController::class);
        Route::apiResource('jne-destinations', JneDestinationController::class);

        Route::apiResource('product-discounts', ProductDiscountController::class)->only(['index', 'destroy']);
        Route::post('product-discounts/batch-update', [ProductDiscountController::class, 'batchUpdate']);
        Route::post('product-discounts/batch-destroy', [ProductDiscountController::class, 'batchDestroy']);

        Route::apiResource('notification-types', NotificationTypeController::class);
        Route::apiResource('notification-channels', NotificationChannelController::class);

        Route::apiResource('fixed-prices', FixedPriceController::class)->only(['index', 'destroy']);
        Route::post('fixed-prices/batch-update', [FixedPriceController::class, 'batchUpdate']);
        Route::post('fixed-prices/batch-destroy', [FixedPriceController::class, 'batchDestroy']);
        Route::get('orders/export/csv', [OrderController::class, 'exportCsv'])->name('orders.export.csv');
        Route::get('payments/export/csv', [PaymentController::class, 'exportCsv'])->name('payments.export.csv');
    });

    Route::prefix('cart')->name('cart.')->group(function () {
        Route::get('/', [CartController::class, 'index'])->name('index');
        Route::post('/', [CartController::class, 'store'])->name('store');
        Route::get('/summary', [CartController::class, 'summary'])->name('summary');
        Route::delete('/clear', [CartController::class, 'clear'])->name('clear');
        Route::delete('/multiple', [CartController::class, 'destroyMultiple'])->name('destroy-multiple');
        Route::get('/{cartItem}', [CartController::class, 'show'])->name('show');
        Route::put('/{cartItem}', [CartController::class, 'update'])->name('update');
        Route::delete('/{cartItem}', [CartController::class, 'destroy'])->name('destroy');
    });

    Route::prefix('orders')->name('orders.')->group(function () {
        Route::get('/', [OrderController::class, 'index'])->name('index');
        Route::post('/', [OrderController::class, 'store'])->name('store');
        Route::delete('/multiple', [OrderController::class, 'destroyMultiple'])->name('destroy-multiple');
        Route::get('/contact/{contactId}', [OrderController::class, 'getByContact'])->name('by-contact');
        Route::get('/order-id/{orderId}', [OrderController::class, 'getByOrderId'])->name('by-order-id');
        Route::get('/{order}', [OrderController::class, 'show'])->name('show');
        Route::put('/{order}', [OrderController::class, 'update'])->name('update');
        Route::delete('/{order}', [OrderController::class, 'destroy'])->name('destroy');
        Route::post('/generate-jne-airwaybill', [OrderController::class, 'generateJneAirwaybill'])->name('generate-jne-airwaybill');
        Route::post('/generate-jubelio-shipment-airwaybill', [OrderController::class, 'generateJubelioShipmentAirwaybill'])->name('generate-jubelio-shipment-airwaybill');
    });

    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [NotificationController::class, 'index'])->name('index');
        Route::get('/unread-count', [NotificationController::class, 'unreadCount'])->name('unread-count');
        Route::post('/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
        Route::delete('/clear', [NotificationController::class, 'clear'])->name('clear');
        Route::post('/{id}/mark-read', [NotificationController::class, 'markAsRead'])->name('mark-read');
        Route::delete('/{id}', [NotificationController::class, 'destroy'])->name('destroy');
        Route::get('/stream', [NotificationController::class, 'stream']);
    });

    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/notifications', [UserNotificationController::class, 'index'])->name('notifications.index');
        Route::post('/notifications', [UserNotificationController::class, 'store'])->name('notifications.store');
        Route::get('/notification-channels', [UserNotificationController::class, 'getNotificationChannels'])->name('notification-channels.index');
    });

    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/logout-all', [AuthController::class, 'logoutAll']);
    Route::get('/jubelio-refresh-token', [AuthController::class, 'getJubelioToken']);

    Route::post('product-discounts/batch', [ProductDiscountController::class, 'batchIndex']);
    Route::post('fixed-prices/batch', [FixedPriceController::class, 'batchIndex']);

    // Profile routes
    Route::get('/profile', [UserController::class, 'profile']);
    Route::put('/profile', [UserController::class, 'updateProfile']);
    Route::post('/change-password', [UserController::class, 'changePassword']);

    Route::apiResource('jne-branches', JneBranchController::class)->only(['index', 'show']);
    Route::apiResource('jne-origins', JneOriginController::class)->only(['index', 'show']);
    Route::apiResource('jne-destinations', JneDestinationController::class)->only(['index', 'show']);

    Route::apiResource('shipping-addresses', ShippingAddressController::class);
    Route::delete('shipping-addresses', [ShippingAddressController::class, 'destroyMultiple']);

    Route::apiResource('sender-addresses', SenderAddressController::class);
    Route::delete('sender-addresses', [SenderAddressController::class, 'destroyMultiple']);

    Route::post('/jubelio/sales-order', [JubelioController::class, 'updateSalesOrder']);
    Route::post('/jubelio/wms/sales/ready-to-pick', [JubelioController::class, 'setReadyToPick']);

    Route::get('/shipments/regions', [JubelioShipmentController::class, 'getRegions'])->name('shipments.get-regions');
    Route::get('/shipments/service-categories', [JubelioShipmentController::class, 'getServiceCategories'])->name('shipments.get-service-categories');
    Route::post('/shipments/rates', [JubelioShipmentController::class, 'getRates'])->name('shipments.get-rates');
    Route::get('/shipments/log/{order}', [JubelioShipmentController::class, 'getShipmentLog'])->name('shipments.get-log');

});

