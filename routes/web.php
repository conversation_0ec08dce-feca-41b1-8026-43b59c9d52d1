<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('otoapi');
});

// Route::get('/test', function () {
//     return view('welcome');
// });

// Redirect any other web routes to API documentation or 404
Route::fallback(function () {
    return response()->json([
        'message' => 'API endpoint not found. Please check the API documentation.',
        'status' => 404
    ], 404);
});
