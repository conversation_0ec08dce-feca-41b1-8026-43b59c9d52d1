<?php

use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\MaintenanceMode;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->redirectGuestsTo(function ($request) {
            if ($request->expectsJson()) {
                return null; // Don't redirect, let it throw exception
            }
            return route('login'); // Only redirect for web requests
        });

        $middleware->alias([
            'admin' => AdminMiddleware::class,
            'maintenance' => MaintenanceMode::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (\Throwable $e, $request) {
            if ($request->is('api/*')) {
                if ($e instanceof \Illuminate\Validation\ValidationException) {
                    return response()->json([
                        'message' => 'Validation failed',
                        'errors' => $e->errors()
                    ], 422);
                }

                if ($e instanceof \Illuminate\Auth\Access\AuthorizationException) {
                    return response()->json(['message' => 'This action is unauthorized.'], 403);
                }

                if ($e instanceof \Illuminate\Auth\AuthenticationException) {
                    return response()->json(['message' => 'Unauthenticated.'], 401);
                }

                if ($e instanceof \Symfony\Component\HttpKernel\Exception\NotFoundHttpException) {
                    return response()->json(['message' => 'Resource not found.'], 404);
                }

                // For any other exception, return a generic 500 error
                return response()->json([
                    'message' => "We're sorry, something went wrong. Our team has been notified and is working to fix it.",
                    'error' => $e->getMessage() // Optionally include for debugging
                ], 500);
            }
        });
    })->create();
