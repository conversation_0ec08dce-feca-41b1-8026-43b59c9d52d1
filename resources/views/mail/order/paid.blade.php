@component('mail::message')
{{-- Header --}}
@slot('header')
@component('mail::header', ['url' => config('app.url')])
{{ config('app.name') }}
@endcomponent
@endslot

{{-- Body --}}
# Payment Successful!

Hi **{{ $order->user_name }}**,

Thank you for your payment. Your order **#{{ $order->id }}** has been confirmed and will be processed shortly. We appreciate your business!

---

### **Order Summary**

<table style="width: 100%; margin-bottom: 20px; border-collapse: collapse;">
    <tr>
        <td style="padding: 5px 0;"><strong>Order Number:</strong></td>
        <td style="padding: 5px 0; text-align: right;">#{{ $order->id }}</td>
    </tr>
    <tr>
        <td style="padding: 5px 0;"><strong>Payment Date:</strong></td>
        <td style="padding: 5px 0; text-align: right;">{{ $payment->paid_at->format('d F Y, H:i') }}</td>
    </tr>
    <tr>
        <td style="padding: 5px 0;"><strong>Payment Method:</strong></td>
        <td style="padding: 5px 0; text-align: right;">{{ $payment->payment_channel }}</td>
    </tr>
</table>

---

### **Order Details**

@component('mail::table')
| &nbsp; | Item | Qty | Price | Total |
|:---:|:---|:---:|:---:|---:|
@foreach ($order->items as $item)
| <img src="{{ $item->image }}" alt="{{ $item->name }}" width="60"> | <small style="display: block; width: 250px;">{{ $item->name }}</small> <br> <small>{{ $item->sku }}</small> | {{ $item->quantity }} | Rp {{ number_format($item->price, 0, ',', '.') }} | Rp {{ number_format($item->total_price, 0, ',', '.') }} |
@endforeach
@endcomponent

@component('mail::table')
| | |
|:---|---:|
| **Subtotal** | Rp {{ number_format($order->sub_total, 0, ',', '.') }} |
| **Total Discount** | - Rp {{ number_format($order->total_discount, 0, ',', '.') }} |
| **Shipping Cost** | Rp {{ number_format($order->shipping_cost, 0, ',', '.') }} |
| **Grand Total** | **Rp {{ number_format($order->grand_total, 0, ',', '.') }}** |
@endcomponent

@component('mail::button', ['url' => $url, 'color' => 'success'])
View Your Order
@endcomponent

If you have any questions, feel free to contact our customer support.

Thanks,<br>
{{ config('app.name') }}

{{-- Footer --}}
@slot('footer')
@component('mail::footer')
© {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
@endcomponent
@endslot
@endcomponent
