@component('mail::message')
{{-- Header --}}
@slot('header')
@component('mail::header', ['url' => config('app.url')])
{{ config('app.name') }}
@endcomponent
@endslot

{{-- Body --}}
# Order Completed!

Hi **{{ $order->user_name }}**,

Thank you for your order! Your order **#{{ $order->id }}** has been successfully completed and processed. We appreciate your business and trust in our service.

---

### **Shipping Information**

<table style="width: 100%; margin-bottom: 20px; border-collapse: collapse;">
    <tr>
        <td style="padding: 5px 0;"><strong>Order Number:</strong></td>
        <td style="padding: 5px 0; text-align: right;">#{{ $order->id }}</td>
    </tr>
    <tr>
        <td style="padding: 5px 0;"><strong>Completed Date:</strong></td>
        <td style="padding: 5px 0; text-align: right;">{{ $order->updated_at->format('d F Y, H:i') }}</td>
    </tr>
    @if($order->tracking_number)
    <tr>
        <td style="padding: 5px 0;"><strong>Tracking Number:</strong></td>
        <td style="padding: 5px 0; text-align: right; font-family: monospace; font-weight: bold;">{{ $order->tracking_number }}</td>
    </tr>
    @endif
    @if($order->expedition && is_array($order->expedition))
    <tr>
        <td style="padding: 5px 0;"><strong>Expedition:</strong></td>
        <td style="padding: 5px 0; text-align: right;">{{ $order->expedition['name'] ?? 'N/A' }}</td>
    </tr>
    @if(isset($order->expedition['service']))
    <tr>
        <td style="padding: 5px 0;"><strong>Service:</strong></td>
        <td style="padding: 5px 0; text-align: right;">{{ $order->expedition['service'] }}</td>
    </tr>
    @endif
    @if(isset($order->expedition['etd']))
    <tr>
        <td style="padding: 5px 0;"><strong>Estimated Delivery:</strong></td>
        <td style="padding: 5px 0; text-align: right;">{{ $order->expedition['etd'] }} day(s)</td>
    </tr>
    @endif
    @endif
</table>

---

### **Shipping Address**

<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
    <strong>{{ $order->shipping_full_name }}</strong><br>
    {{ $order->shipping_phone }}<br>
    {{ $order->shipping_address }}<br>
    {{ $order->shipping_subdistrict }}, {{ $order->shipping_district }}<br>
    {{ $order->shipping_city }}, {{ $order->shipping_province }} {{ $order->shipping_post_code }}
</div>

---

### **Order Details**

@component('mail::table')
| &nbsp; | Item | Qty | Price | Total |
|:---:|:---|:---:|:---:|---:|
@foreach ($order->items as $item)
| <img src="{{ $item->image }}" alt="{{ $item->name }}" width="60"> | <small style="display: block; width: 250px; color: #333;">{{ $item->name }}</small> <small style="color: #888;">SKU: {{ $item->sku }}</small> | {{ $item->quantity }} | Rp {{ number_format($item->price, 0, ',', '.') }} | Rp {{ number_format($item->total_price, 0, ',', '.') }} |
@endforeach
@endcomponent

@component('mail::table')
| | |
|:---|---:|
| **Subtotal** | Rp {{ number_format($order->sub_total, 0, ',', '.') }} |
| **Total Discount** | - Rp {{ number_format($order->total_discount, 0, ',', '.') }} |
| **Shipping Cost** | Rp {{ number_format($order->shipping_cost, 0, ',', '.') }} |
| **Grand Total** | **Rp {{ number_format($order->grand_total, 0, ',', '.') }}** |
@endcomponent

@component('mail::button', ['url' => $url, 'color' => 'primary'])
View Order Details
@endcomponent

@if($order->tracking_number)
Your order has been shipped with tracking number: **{{ $order->tracking_number }}**. You can use this number to track your package with the courier.
@endif

Thank you for choosing us! If you have any questions about your order, feel free to contact our customer support.

Thanks,<br>
{{ config('app.name') }}

{{-- Footer --}}
@slot('footer')
@component('mail::footer')
© {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
@endcomponent
@endslot
@endcomponent
