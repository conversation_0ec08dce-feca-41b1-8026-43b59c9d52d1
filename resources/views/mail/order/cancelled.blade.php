@component('mail::message')
{{-- Header --}}
@slot('header')
@component('mail::header', ['url' => config('app.url')])
{{ config('app.name') }}
@endcomponent
@endslot

{{-- Body --}}
# Order Cancelled

Assalaamu'alaikum **{{ $order->user_name }}**,

We are writing to inform you that your order **#{{ $order->id }}** has been cancelled.

@if ($reason)
**Reason for cancellation:** {{ $reason }}
@endif

If you did not request this cancellation, please contact our customer support for assistance.

---

### **Order Summary**

<table style="width: 100%; margin-bottom: 20px; border-collapse: collapse;">
    <tr>
        <td style="padding: 5px 0;"><strong>Order Number:</strong></td>
        <td style="padding: 5px 0; text-align: right;">#{{ $order->id }}</td>
    </tr>
    <tr>
        <td style="padding: 5px 0;"><strong>Cancellation Date:</strong></td>
        <td style="padding: 5px 0; text-align: right;">{{ $order->updated_at->format('d F Y, H:i') }}</td>
    </tr>
</table>

---

### **Order Details**

@component('mail::table')
| &nbsp; | Item | Qty | Price | Total |
|:---:|:---|:---:|:---:|---:|
@foreach ($order->items as $item)
| <img src="{{ $item->image }}" alt="{{ $item->name }}" width="60"> | <small style="display: block; width: 250px; color: #333;">{{ $item->name }}</small> <small style="color: #888;">SKU: {{ $item->sku }}</small> | {{ $item->quantity }} | Rp {{ number_format($item->price, 0, ',', '.') }} | Rp {{ number_format($item->total_price, 0, ',', '.') }} |
@endforeach
@endcomponent

@component('mail::table')
| | |
|:---|---:|
| **Subtotal** | Rp {{ number_format($order->sub_total, 0, ',', '.') }} |
| **Total Discount** | - Rp {{ number_format($order->total_discount, 0, ',', '.') }} |
| **Shipping Cost** | Rp {{ number_format($order->shipping_cost, 0, ',', '.') }} |
| **Insurance Cost** | Rp {{ number_format($order->insurance_cost, 0, ',', '.') }} |
| **Grand Total** | **Rp {{ number_format($order->grand_total, 0, ',', '.') }}** |
@endcomponent

@component('mail::button', ['url' => $url, 'color' => 'primary'])
View Order Details
@endcomponent

We apologize for any inconvenience this may have caused.

Thanks,<br>
{{ config('app.name') }}

{{-- Footer --}}
@slot('footer')
@component('mail::footer')
© {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
@endcomponent
@endslot
@endcomponent
