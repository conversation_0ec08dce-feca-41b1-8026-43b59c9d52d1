<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class Jubelio
{
    protected $base_url;
    protected $email;
    protected $password;
    protected $secret_key;

    public function __construct()
    {
        $this->base_url = config('jubelio.base_url');
        $this->email = config('jubelio.email');
        $this->password = config('jubelio.password');
        $this->secret_key = config('jubelio.secret_key');
    }

    /**
     * Login to get token
     *
     * @return string|null
     */
    public function loginToGetToken()
    {
        $url = $this->base_url . '/login';
        $response = Http::post($url, [
            'email' => $this->email,
            'password' => $this->password
        ]);

        if ($response->failed()) {
            Log::error('Login failed: ' . $response->body());
            return null;
        }

        $data = $response->json();
        // Log::info('Login Response:', $data);
        $token = $data['token'];
        // Log::info('Create new Jubelio Token');

        Cache::put('jubelio_token', $token, now()->addHours(11));

        return $token;
    }

    /**
     * Get token from cache or login to get token
     *
     * @return string
     */
    function getToken()
    {
        $token = Cache::get('jubelio_token');

        if (!$token) {
            $token = $this->loginToGetToken();
        }

        return $token;
    }

    /**
     * Get a pre-configured HTTP client instance for Jubelio.
     *
     * @return \Illuminate\Http\Client\PendingRequest
     */
    private function client()
    {
        return Http::withToken($this->getToken())
            ->baseUrl($this->base_url)
            ->contentType('application/json')
            ->acceptJson();
    }

    /**
     * Set an order as paid in Jubelio.
     *
     * @param int $jubelioOrderId
     * @return array|null
     */
    public function setOrderAsPaid(int $jubelioOrderId): ?array
    {
        try {
            $payload = ['ids' => [(string) $jubelioOrderId]];
            $response = $this->client()->post('/sales/orders/set-as-paid', $payload);

            $responseData = $response->json();
            Log::info("Successfully set order {$jubelioOrderId} as paid in Jubelio.", [
                'request' => $payload,
                'response' => $responseData,
            ]);
            return $responseData;
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error("Failed to set order {$jubelioOrderId} as paid in Jubelio.", [
                'request' => ['ids' => [(string) $jubelioOrderId]],
                'response_status' => $e->response->status(),
                'response_body' => $e->response->json() ?? $e->response->body(),
            ]);
            return null;
        }
    }

    /**
     * Get sales order detail from Jubelio.
     *
     * @param int $jubelioOrderId
     * @return array|null
     */
    public function getSalesOrderDetail(int $jubelioOrderId): ?array
    {
        try {
            $response = $this->client()->get("/sales/orders/{$jubelioOrderId}");

            $responseData = $response->json();
            Log::info("Successfully retrieved sales order {$jubelioOrderId} from Jubelio.", [
                'response' => $responseData,
            ]);
            return $responseData;
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error("Failed to retrieve sales order {$jubelioOrderId} from Jubelio.", [
                'response_status' => $e->response->status(),
                'response_body' => $e->response->json() ?? $e->response->body(),
            ]);
            return null;
        }
    }

    /**
     * Create or update a sales order in Jubelio.
     * The same endpoint is used for both operations.
     *
     * @param array $salesOrderData
     * @return array|null
     */
    public function createOrUpdateSalesOrder(array $salesOrderData): ?array
    {
        $isUpdate = !empty($salesOrderData['salesorder_id']);
        // Per user instruction, the endpoint is the same for create and update.
        // No leading slash to prevent double slashes with base_url
        $url = '/sales/orders/';

        try {
            if (!$isUpdate) {
                // Set defaults for creation
                $salesOrderData['salesorder_id'] = 0;
                $salesOrderData['salesorder_no'] = '[auto]';
            }

            $response = $this->client()->post($url, $salesOrderData);

            if ($response->failed()) {
                $action = $isUpdate ? 'update' : 'create';
                $id = $salesOrderData['salesorder_id'] ?? 'N/A';
                Log::error("Failed to {$action} sales order {$id} in Jubelio.", [
                    'request' => $salesOrderData,
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                    'response_headers' => $response->headers(),
                ]);
                return null;
            }

            $responseData = $response->json();

            if ($isUpdate) {
                Log::info("Successfully updated sales order {$salesOrderData['salesorder_id']} in Jubelio.", [
                    'request' => $salesOrderData,
                    'response' => $responseData,
                ]);
            } else {
                Log::info("Successfully created a new sales order in Jubelio.", [
                    'request' => $salesOrderData,
                    'response' => $responseData,
                ]);
            }

            return $responseData;
        } catch (\Exception $e) {
            $action = $isUpdate ? 'update' : 'create';
            $id = $salesOrderData['salesorder_id'] ?? 'N/A';
            Log::error("Exception when trying to {$action} sales order {$id} in Jubelio.", [
                'request' => $salesOrderData,
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Set sales orders as ready to pick in Jubelio.
     *
     * @param array $salesOrderIds
     * @return array|null
     */
    public function setReadyToPick(array $salesOrderIds): ?array
    {
        try {
            // Ensure all IDs are integers.
            $payload = ['salesorder_ids' => array_map('intval', $salesOrderIds)];
            $response = $this->client()->post('/wms/sales/ready-to-pick', $payload);

            if ($response->failed()) {
                Log::error('Failed to set sales orders as ready to pick in Jubelio.', [
                    'request' => $payload,
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('Successfully set sales orders as ready to pick in Jubelio.', [
                'request' => $payload,
                'response' => $responseData,
            ]);
            return $responseData;
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error('RequestException when setting sales orders as ready to pick in Jubelio.', [
                'request' => ['salesorder_ids' => array_map('strval', $salesOrderIds)],
                'response_status' => $e->response->status(),
                'response_body' => $e->response->json() ?? $e->response->body(),
            ]);
            return null;
        } catch (\Exception $e) {
            Log::error('Exception when setting sales orders as ready to pick in Jubelio.', [
                'request' => ['salesorder_ids' => array_map('strval', $salesOrderIds)],
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Check if a sales order is ready to be processed in Jubelio.
     *
     * @param int $jubelioOrderId
     * @return bool
     */
    public function isOrderReadyToProcess(int $jubelioOrderId): bool
    {
        try {
            $response = $this->client()->get('/wms/sales/orders/ready-to-process/', [
                'page' => 1,
                'pageSize' => 1,
                'q' => $jubelioOrderId,
            ]);

            if ($response->failed()) {
                Log::warning("Jubelio API check for ready-to-process failed for order {$jubelioOrderId}.", [
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return false;
            }

            $data = $response->json();
            $isReady = isset($data['totalCount']) && $data['totalCount'] > 0;

            Log::info("Jubelio ready-to-process check for order {$jubelioOrderId}: " . ($isReady ? 'Ready' : 'Not Ready'), [
                'response' => $data,
            ]);

            return $isReady;
        } catch (\Exception $e) {
            Log::error("Exception during Jubelio ready-to-process check for order {$jubelioOrderId}.", [
                'exception_message' => $e->getMessage(),
            ]);
            return false;
        }
    }
    /**
     * Verify the webhook signature from Jubelio.
     *
     * @param \Illuminate\Http\Request $request
     * @return bool
     */
    public function verifyWebhookSignature(\Illuminate\Http\Request $request): bool
    {
        $jubelioSignature = $request->header('sign');

        if (!$jubelioSignature) {
            Log::warning('Jubelio webhook signature not found.');
            return false;
        }

        $payload = trim($request->getContent());
        $content = $payload . $this->secret_key;
        $calculatedSignature = hash_hmac('sha256', $content, $this->secret_key, false);
        $isValid = hash_equals($calculatedSignature, $jubelioSignature);
        $payloadArray = json_decode($payload, true);

        Log::info('Jubelio Webhook Signature Verification Success', [
            'store_name' => $payloadArray['store_name'] ?? 'not_found',
            'channel_status' => $payloadArray['channel_status'] ?? 'not_found',
        ]);

        if (!$isValid) {
            Log::warning('Jubelio webhook signature validation failed.');
        }

        return $isValid;
    }

    /**
     * Create a new user in Jubelio.
     *
     * @param \App\Models\User $user
     * @return array|null
     */
    public function createUser(User $user): ?array
    {
        $payload = [
            'contact_id' => 0,
            'contact_name' => $user->name,
            'contact_type' => 0, // 0 for customer default
            'phone' => $user->phone,
            'email' => $user->email,
            'is_reseller' => $user->role === 'reseller', // is_reseller must true to keep track data
        ];

        try {
            $response = $this->client()->post('/contacts/', $payload);

            if ($response->failed()) {
                Log::error("Failed to create contact in Jubelio for user: {$user->email}", [
                    'request' => $payload,
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info("Successfully created contact in Jubelio for user: {$user->email}", [
                'request' => $payload,
                'response' => $responseData,
            ]);

            return $responseData;

        } catch (\Exception $e) {
            Log::error("Exception when trying to create contact in Jubelio for user: {$user->email}", [
                'request' => $payload,
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Create or update a contact in Jubelio.
     *
     * @param array $contactData The contact data from our system.
     * @param int|null $jubelioContactId The existing Jubelio contact ID for updates.
     * @return array|null
     */
    public function createOrUpdateContact(array $contactData, ?int $jubelioContactId = null): ?array
    {
        $isUpdate = $jubelioContactId !== null;

        $payload = [
            'contact_id' => $isUpdate ? $jubelioContactId : 0,
            'contact_type' => 0, // 0 for customer default
            'is_reseller' => true, // Always true as per user instruction
        ];

        // Dynamically build the payload to handle partial updates correctly.
        // Only add fields to the payload if they exist in the input data.
        if (array_key_exists('name', $contactData)) {
            $payload['contact_name'] = $contactData['name'];
        }
        if (array_key_exists('phone', $contactData)) {
            $payload['phone'] = $contactData['phone'];
        }
        if (array_key_exists('email', $contactData)) {
            $payload['email'] = $contactData['email'];
        }

        // For creation, Jubelio might require all fields. If any are missing,
        // we ensure they are present in the payload, even if null.
        if (!$isUpdate) {
            $payload['contact_name'] = $contactData['name'] ?? null;
            $payload['phone'] = $contactData['phone'] ?? null;
            $payload['email'] = $contactData['email'] ?? null;
        }

        try {
            $response = $this->client()->post('/contacts/', $payload);

            if ($response->failed()) {
                $action = $isUpdate ? 'update' : 'create';
                Log::error("Failed to {$action} contact in Jubelio.", [
                    'request' => $payload,
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            $action = $isUpdate ? 'Updated' : 'Created';
            Log::info("Successfully {$action} contact in Jubelio: {$payload['contact_name']}", [
                'request' => $payload,
                'response' => $responseData,
            ]);

            return $responseData;

        } catch (\Exception $e) {
            $action = $isUpdate ? 'update' : 'create';
            Log::error("Exception when trying to {$action} contact in Jubelio.", [
                'request' => $payload,
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }
    /**
     * Delete a contact in Jubelio.
     *
     * @param int $contactId The Jubelio contact ID.
     * @return array|null
     */
    public function deleteContact(int $contactId): ?array
    {
        try {
            $payload = ['ids' => [$contactId]];
            $response = $this->client()->delete('/contacts/pelanggan', $payload);

            if ($response->failed()) {
                Log::error("Failed to delete contact in Jubelio.", [
                    'request' => $payload,
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info("Successfully deleted contact {$contactId} in Jubelio.", [
                'request' => $payload,
                'response' => $responseData,
            ]);

            return $responseData;

        } catch (\Exception $e) {
            Log::error("Exception when trying to delete contact {$contactId} in Jubelio.", [
                'request' => ['ids' => [$contactId]],
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }
}
