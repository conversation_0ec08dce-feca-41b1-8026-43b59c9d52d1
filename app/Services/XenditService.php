<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\User;
use Xendit\Configuration;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Xendit\Invoice\CreateInvoiceRequest;
use Xendit\Invoice\InvoiceApi;
use Xendit\PaymentRequest\PaymentMethodReusability;
use Xendit\PaymentRequest\PaymentMethodType;
use Xendit\PaymentRequest\PaymentRequest;
use Xendit\PaymentRequest\PaymentRequestApi;
use Xendit\PaymentRequest\PaymentRequestCurrency;
use Xendit\PaymentRequest\PaymentRequestParameters;
use Xendit\PaymentRequest\VirtualAccountChannelCode;
use Xendit\PaymentRequest\VirtualAccountChannelProperties;
use Xendit\PaymentRequest\VirtualAccountParameters;
use Xendit\XenditSdkException;

class XenditService
{
    private $otoresellUrl;
    private $secretKey;
    private $baseUrl;
    private $invoiceWebhookUrl;


    public function __construct()
    {
        $this->secretKey = config('xendit.secret_key');
        $this->baseUrl = config('xendit.base_url');
        $this->invoiceWebhookUrl = config('app.url') . '/api/webhook/xendit/invoice';
        // $this->invoiceWebhookUrl = 'https://95d6b51994ac.ngrok-free.app/otoapi/public/api/webhook/xendit/invoice';
        $this->otoresellUrl = config('frontend.otoresell_url');
        Configuration::setXenditKey($this->secretKey);
    }

    public function createInvoiceXendit(Order $order, Payment $payment)
    {
        $apiInstance = new InvoiceApi();

        $paymentMethods = [];
        if ($order->payment_method === 'qr_code') {
            $paymentMethods = ['QRIS'];
        } elseif ($order->payment_method === 'bank_transfer') {
            $paymentMethods = ['BNI', 'BRI', 'BSI', 'CIMB', 'MANDIRI', 'PERMATA'];
        }

        $items = $order->items->map(function (OrderItem $item) {
            return [
                'name' => $item->name,
                'quantity' => $item->quantity,
                'price' => (int) $item->price,
                'reference_id' => (string) $item->jubelio_item_id,
            ];
        })->toArray();

        $create_invoice_request = new CreateInvoiceRequest([
            'external_id' => $payment->external_id,
            'amount' => $order->grand_total,
            'payer_email' => $order->user->email,
            'description' => 'Payment for Order #' . $order->jubelio_order_id,
            // 'invoice_duration' => 86400, // for testing
            // 'invoice_duration' => 900, // for testing
            'invoice_duration' => 1800, // real
            'success_redirect_url' => $this->otoresellUrl . '/cart/success?orderId=' . $order->jubelio_order_id,
            'failure_redirect_url' => $this->otoresellUrl . '/cart/checkout',
            'payment_methods' => $paymentMethods,
            'currency' => 'IDR',
            'items' => $items,
            // 'reminder_time_unit' => 'hours',
            // 'reminder_time' => 1,
        ]);

        try {
            $result = $apiInstance->createInvoice($create_invoice_request);
            Log::info('Invoice created: ' . json_encode($result));
            return $result;
        } catch (XenditSdkException $e) {
            Log::error('Exception when calling InvoiceApi->createInvoice: ' . $e->getMessage());
            Log::error('Full Error: ' . json_encode($e->getFullError()));
            throw $e;
        }
    }

    private function generateBearerToken()
    {
        // Xendit uses Basic Auth with the secret key as the username and an empty password.
        return 'Basic ' . base64_encode($this->secretKey . ':');
    }

    public function baseRequest(string $method, string $endpoint, array $data = [])
    {
        $url = $this->baseUrl . $endpoint;

        $response = Http::withHeaders([
            'Authorization' => $this->generateBearerToken(),
            'Content-Type' => 'application/json',
        ])->{$method}($url, $data);

        if ($response->failed()) {
            Log::error('Xendit API request failed', [
                'url' => $url,
                'method' => $method,
                'data' => $data,
                'response_status' => $response->status(),
                'response_body' => $response->body(),
            ]);
            $response->throw();
        }

        Log::info('Xendit API request successful', [
            'url' => $url,
            'method' => $method,
            'data' => $data,
            'response_status' => $response->status(),
        ]);

        return $response->json();
    }

    public function setInvoiceWebhookUrl()
    {
        Log::info('Setting Xendit invoice webhook URL to: ' . $this->invoiceWebhookUrl);

        try {
            $response = $this->baseRequest('post', '/callback_urls/invoice', [
                'url' => $this->invoiceWebhookUrl,
            ]);

            Log::info('Xendit set invoice webhook response: ' . json_encode($response));
            return $response;
        } catch (\Exception $e) {
            Log::error('Failed to set Xendit invoice webhook URL.', [
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }
}
