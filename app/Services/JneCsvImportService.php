<?php

namespace App\Services;

use App\Models\JneBranch;
use App\Models\JneDestination;
use App\Models\JneOrigin;
use Illuminate\Console\OutputStyle;
use Illuminate\Support\Facades\DB;
use Spatie\SimpleExcel\SimpleExcelReader;
use Illuminate\Support\Facades\Log;

class JneCsvImportService
{
    protected ?OutputStyle $output;

    public function setOutput(OutputStyle $output): self
    {
        $this->output = $output;
        return $this;
    }

    /**
     * Import JNE branches from a CSV file.
     */
    public function importBranches(string $filePath, ?callable $progressCallback = null): void
    {
        $this->import(
            $filePath,
            'Importing JNE Branches',
            JneBranch::class,
            ['branch_code'],
            ['name'],
            [
                'branch_code' => 'Branch code',
                'name' => 'Branch name',
            ],
            $progressCallback
        );
    }

    /**
     * Import JNE origins from a CSV file.
     */
    public function importOrigins(string $filePath, ?callable $progressCallback = null): void
    {
        $this->import(
            $filePath,
            'Importing JNE Origins',
            JneOrigin::class,
            ['origin_code'],
            ['name'],
            [
                'origin_code' => 'Origin code',
                'name' => 'Origin name',
            ],
            $progressCallback
        );
    }

    /**
     * Import JNE destinations from a CSV file.
     */
    public function importDestinations(string $filePath, ?callable $progressCallback = null): void
    {
        $this->import(
            $filePath,
            'Importing JNE Destinations',
            JneDestination::class,
            ['country_name', 'province_name', 'city_name', 'district_name', 'subdistrict_name', 'zip_code'],
            ['tariff_code'],
            [
                'country_name' => 'COUNTRY_NAME',
                'province_name' => 'PROVINCE_NAME',
                'city_name' => 'CITY_NAME',
                'district_name' => 'DISTRICT_NAME',
                'subdistrict_name' => 'SUBDISTRICT_NAME',
                'zip_code' => 'ZIP_CODE',
                'tariff_code' => 'TARIFF_CODE',
            ],
            $progressCallback
        );
    }

    /**
     * Generic import logic for a CSV file.
     */
    protected function import(string $filePath, string $progressTitle, string $modelClass, array $uniqueBy, array $update, array $columnMap, ?callable $progressCallback = null): void
    {
        if (!file_exists($filePath)) {
            $this->output?->error("File not found: {$filePath}");
            return;
        }

        $this->output?->newLine(2);
        $this->output?->title($progressTitle);

        // Use DB transaction for data integrity
        DB::transaction(function () use ($filePath, $modelClass, $uniqueBy, $update, $columnMap, $progressCallback) {
            $rows = SimpleExcelReader::create($filePath)->getRows();

            $rows->chunk(1000)->each(function ($chunk) use ($modelClass, $uniqueBy, $update, $columnMap, $progressCallback) {
                $originalChunkCount = $chunk->count();

                $dataToUpsert = $chunk->map(function ($row) use ($columnMap, $uniqueBy) {
                    $mappedRow = [];
                    foreach ($columnMap as $dbColumn => $csvHeader) {
                        $value = $row[$csvHeader] ?? null;

                        // Normalize the unique key fields
                        if (in_array($dbColumn, $uniqueBy) && is_string($value)) {
                            $value = strtoupper(trim($value)); // Convert to uppercase and trim whitespace
                        } elseif (is_string($value)) {
                            $value = trim($value); // For other string fields, just trim whitespace
                        }

                        $mappedRow[$dbColumn] = $value;
                    }
                    return $mappedRow;
                })->filter(function ($mappedRow) {
                    $isValid = !empty(array_filter($mappedRow));
                    if (!$isValid) {
                        Log::warning('JNE Import: Filtered out an empty or invalid row.', ['row' => $mappedRow]);
                    }
                    return $isValid;
                })->toArray();

                // --- START: New Duplicate Logging Logic ---
                $uniqueSignatures = [];
                $duplicatesInChunk = [];
                $deduplicatedData = [];

                foreach ($dataToUpsert as $row) {
                    // Create a consistent signature from the unique key columns
                    $signatureParts = [];
                    foreach ($uniqueBy as $key) {
                        $signatureParts[] = $row[$key] ?? '';
                    }
                    $signature = implode('|', $signatureParts);

                    if (isset($uniqueSignatures[$signature])) {
                        // We found a duplicate within this chunk
                        $duplicatesInChunk[] = $row;
                        // Overwrite the previous one with the current one, as this is what upsert does
                        $uniqueSignatures[$signature] = $row;
                    } else {
                        $uniqueSignatures[$signature] = $row;
                    }
                }

                if (!empty($duplicatesInChunk)) {
                    // For each group of duplicates, find the final record that was kept
                    $groupedDuplicates = [];
                    foreach ($duplicatesInChunk as $duplicate) {
                        $signatureParts = [];
                        foreach ($uniqueBy as $key) {
                            $signatureParts[] = $duplicate[$key] ?? '';
                        }
                        $signature = implode('|', $signatureParts);
                        $groupedDuplicates[$signature][] = $duplicate;
                    }

                    foreach ($groupedDuplicates as $signature => $duplicates) {
                        $keptRecord = $uniqueSignatures[$signature];
                        Log::warning('JNE Import: Found and discarded duplicate rows within a single chunk.', [
                            'signature' => $signature,
                            'duplicates_discarded' => $duplicates,
                            'record_kept_for_upsert' => $keptRecord,
                        ]);
                    }
                }

                // Use the de-duplicated data for the upsert
                $deduplicatedData = array_values($uniqueSignatures);
                // --- END: New Duplicate Logging Logic ---

                if (!empty($deduplicatedData)) {
                    // IMPORTANT: Use the deduplicatedData array for the upsert
                    $modelClass::upsert($deduplicatedData, $uniqueBy, $update);
                    if ($progressCallback) {
                        // The progress bar should advance by the original count to reflect reality
                        $progressCallback($originalChunkCount);
                    }
                } elseif ($progressCallback) {
                    // Ensure progress bar advances even if chunk was empty/all duplicates
                    $progressCallback($originalChunkCount);
                }
            });
        });

        $this->output?->newLine(2);
        $this->output?->success(class_basename($modelClass) . ' data imported successfully.');
    }
}
