<?php

namespace App\Services;

use App\Models\JneDestination;
use App\Models\JneOrigin;
use App\Models\Order;
use App\Utils\Formatter;
use App\Utils\JubelioPayloadBuilder;
use Illuminate\Support\Facades\Log;

class JneAirwaybillService
{
    protected Jne $jne;
    protected Jubelio $jubelio;

    public function __construct(Jne $jne, Jubelio $jubelio)
    {
        $this->jne = $jne;
        $this->jubelio = $jubelio;
    }

    public function handleJneExpedition(Order $order): array
    {
        try {
            $jnePayload = $this->buildJnePayload($order);
            $jneResponse = $this->jne->generateAirwaybill($jnePayload);
            Log::info('JNE response: ' . json_encode($jneResponse));

            if ($jneResponse && isset($jneResponse['detail'][0]['status']) && $jneResponse['detail'][0]['status'] === 'sukses' && isset($jneResponse['detail'][0]['cnote_no'])) {
                $trackingNumber = $jneResponse['detail'][0]['cnote_no'];
                Log::info('JNE tracking number generated: ' . $trackingNumber);

                $currentSalesOrder = $this->jubelio->getSalesOrderDetail($order->jubelio_order_id);
                if ($currentSalesOrder) {
                    $updatePayload = JubelioPayloadBuilder::buildJubelioUpdatePayload($currentSalesOrder, $trackingNumber);
                    $this->jubelio->createOrUpdateSalesOrder($updatePayload);

                    $order->update(['tracking_number' => $trackingNumber]);

                    return ['success' => true, 'message' => 'JNE airwaybill generated successfully.'];
                }
                return ['success' => false, 'message' => 'Jubelio sales order not found.'];
            }

            $errorMessage = $jneResponse['detail'][0]['reason'] ?? 'Failed to generate JNE airwaybill.';
            return ['success' => false, 'message' => $errorMessage];
        } catch (\Exception $e) {
            Log::error('Failed to generate JNE airwaybill for order #' . $order->id . ': ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    private function buildJnePayload(Order $order): array
    {
        $order->load('items');
        $totalQuantity = $order->items->sum('quantity');
        $totalWeight = $order->items->reduce(function ($total, $item) {
            return $total + ($item->weight ?: 0) * $item->quantity;
        }, 0);
        $totalWeightInKg = ceil($totalWeight / 1000);
        $defaultOrigin = JneOrigin::where('is_default', true)->first();

        $destination = $this->findJneDestination($order);

        return [
            'OLSHOP_ORDERID' => 'OTO-' . $order->id . '-' . now()->timestamp,
            'OLSHOP_SHIPPER_NAME' => $order->sender_full_name ?: config('jne.shipper_name'),
            'OLSHOP_SHIPPER_ADDR1' => $order->sender_address ?: config('jne.shipper_address'),
            'OLSHOP_SHIPPER_ADDR2' => $order->sender_address ?: config('jne.shipper_address'),
            'OLSHOP_SHIPPER_ADDR3' => $order->sender_district ?: 'Ngasem',
            'OLSHOP_SHIPPER_CITY' => $order->sender_city ?: config('jne.shipper_city'),
            'OLSHOP_SHIPPER_REGION' => $order->sender_province ?: config('jne.shipper_province'),
            'OLSHOP_SHIPPER_ZIP' => $order->sender_post_code ?: config('jne.shipper_zip'),
            'OLSHOP_SHIPPER_PHONE' => Formatter::normalizePhoneNumber($order->sender_phone ?: config('jne.shipper_phone')),
            'OLSHOP_RECEIVER_NAME' => $order->shipping_full_name,
            'OLSHOP_RECEIVER_ADDR1' => $order->shipping_address,
            'OLSHOP_RECEIVER_ADDR2' => $order->shipping_subdistrict,
            'OLSHOP_RECEIVER_ADDR3' => $order->shipping_district,
            'OLSHOP_RECEIVER_CITY' => $order->shipping_city,
            'OLSHOP_RECEIVER_REGION' => $order->shipping_province,
            'OLSHOP_RECEIVER_ZIP' => $order->shipping_post_code,
            'OLSHOP_RECEIVER_PHONE' => Formatter::normalizePhoneNumber($order->shipping_phone),
            'OLSHOP_QTY' => $totalQuantity,
            'OLSHOP_WEIGHT' => $totalWeightInKg,
            'OLSHOP_GOODSVALUE' => $order->sub_total,
            'OLSHOP_ORIG' => $defaultOrigin ? $defaultOrigin->origin_code : config('jne.origin_code'),
            'OLSHOP_DEST' => $destination ? $destination->tariff_code : '',
            'OLSHOP_SERVICE' => $order->expedition['service'],
        ];
    }


    private function findJneDestination(Order $order): ?JneDestination
    {
        $normalizedCity = Formatter::normalizeCity($order->shipping_city);

        $queries = [
            [
                'province_name' => $order->shipping_province,
                'city_name' => $normalizedCity,
                'district_name' => $order->shipping_district,
                'subdistrict_name' => $order->shipping_subdistrict,
                'zip_code' => $order->shipping_post_code,
            ],
            [
                'province_name' => $order->shipping_province,
                'city_name' => $normalizedCity,
                'district_name' => $order->shipping_district,
                'subdistrict_name' => $order->shipping_subdistrict,
            ],
            [
                'province_name' => $order->shipping_province,
                'city_name' => $normalizedCity,
                'district_name' => $order->shipping_district,
            ],
            [
                'province_name' => $order->shipping_province,
                'city_name' => $normalizedCity,
            ],
            [
                'province_name' => $order->shipping_province,
            ],
        ];

        foreach ($queries as $query) {
            $destination = JneDestination::where($query)->first();
            if ($destination) {
                return $destination;
            }
        }

        return null;
    }

}
