<?php

namespace App\Services;

use App\Models\Order;
use App\Utils\Formatter;
use App\Utils\JubelioPayloadBuilder;
use Illuminate\Support\Facades\Log;

class AirwaybillService
{
    protected JubelioShipment $jubelioShipment;
    protected Jubelio $jubelio;
    protected string $originSubDistrict;
    protected string $originDistrict;
    protected string $originCity;
    protected string $originProvince;

    public function __construct(JubelioShipment $jubelioShipment, Jubelio $jubelio)
    {
        $this->jubelioShipment = $jubelioShipment;
        $this->jubelio = $jubelio;
        $this->originSubDistrict = config('jne.shipper_sub_district');
        $this->originDistrict = config('jne.shipper_district');
        $this->originCity = config('jne.shipper_city');
        $this->originProvince = config('jne.shipper_province');
    }

    /**
     * Handle the creation of an airwaybill for an expedition order.
     *
     * @param Order $order
     * @return void
     */
    public function handleExpedition(Order $order): void
    {
        try {
            Log::info("Handling expedition for order #{$order->id}");

            $expeditionName = $order->expedition['name'] ?? '';

            if (str_contains($expeditionName, 'J&T Express') || str_contains($expeditionName, 'ID Express')) {
                $Itemcategories = [
                    [
                        'label' => 'Paket',
                        'value' => 'Paket',
                    ],
                    [
                        'label' => 'Dokumen',
                        'value' => 'Dokumen',
                    ],
                ];

                $volumes = [
                    [
                        'length' => 10,
                        'width' => 15,
                        'height' => 5,
                    ]
                ];

                $originDetails = $this->getOriginDetails();
                $destinationDetails = $this->getDestinationDetails($order);

                // Further logic will use $originDetails and $destinationDetails
                $serviceCategories = $this->jubelioShipment->getServiceCategories();
                // Log::info('Service Categories:', ['categories' => $serviceCategories]);

                $rates = $this->getRates($order, $originDetails, $destinationDetails, $Itemcategories, $volumes, $serviceCategories);
                Log::info('Rates:', $rates);

                $result = $this->createShipment($order, $rates, $originDetails, $destinationDetails, $Itemcategories, $volumes);
                Log::info('Shipment Result:', $result);

                if ($result && isset($result['awb'])) {
                    $this->updateOrderWithTrackingNumber($order, $result);
                }
            } else {
                Log::info("Expedition '{$expeditionName}' is not supported yet for order #{$order->id}");
            }
        } catch (\Exception $e) {
            Log::error("Error handling expedition for order #{$order->id}: " . $e->getMessage(), [
                'exception' => $e,
            ]);
        }
    }

    /**
     * Get and log the origin details from Jubelio.
     *
     * @return array|null
     */
    private function getOriginDetails(): ?array
    {
        $originName = "{$this->originSubDistrict}, {$this->originDistrict}, {$this->originCity}, {$this->originProvince}";
        $originDetails = $this->jubelioShipment->getRegions($originName);
        Log::info('Origin Details:', ['name' => $originName, 'details' => $originDetails]);

        return $originDetails;
    }

    /**
     * Get and log the destination details from Jubelio based on the order.
     *
     * @param Order $order
     * @return array|null
     */
    private function getDestinationDetails(Order $order): ?array
    {
        $destinationName = "{$order->shipping_subdistrict}, {$order->shipping_district}, {$order->shipping_city}, {$order->shipping_province}";
        $destinationDetails = $this->jubelioShipment->getRegions($destinationName);
        Log::info('Destination Details:', ['name' => $destinationName, 'details' => $destinationDetails]);

        return $destinationDetails;
    }

    /**
     * Get rates from Jubelio.
     *
     * @param Order $order
     * @param array|null $originDetails
     * @param array|null $destinationDetails
     * @param array $categories
     * @param array $volumes
     * @param array|null $serviceCategories
     * @return array|null
     */
    private function getRates(Order $order, ?array $originDetails, ?array $destinationDetails, array $categories, array $volumes, ?array $serviceCategories): ?array
    {
        if (empty($originDetails[0]) || empty($destinationDetails[0])) {
            Log::error('AirwaybillService: Missing origin or destination details for getRates.');
            return null;
        }

        $origin = $originDetails[0];
        $destination = $destinationDetails[0];

        $items = [];
        $volume = $volumes[0] ?? ['length' => 10, 'width' => 15, 'height' => 5]; // Default volume
        $category = $categories[0] ?? ['label' => 'Paket', 'value' => 'Paket']; // Default category

        foreach ($order->items as $item) {
            $itemName = collect($item->variant)->pluck('value')->implode(', ');
            if (empty($itemName)) {
                $itemName = $item->name;
            }

            $items[] = [
                'item_name' => $itemName,
                'value' => (float) $item->price,
                'quantity' => (int) $item->quantity,
                'length' => $volume['length'],
                'width' => $volume['width'],
                'height' => $volume['height'],
                'weight' => (int) $item->weight,
                'category' => $category,
            ];
        }

        $payload = [
            'origin' => [
                'area_id' => $origin['area_id'],
                'zipcode' => $origin['zipcode'],
                'coordinate' => null,
            ],
            'destination' => [
                'area_id' => $destination['area_id'],
                'zipcode' => $destination['zipcode'],
            ],
            'service_category_id' => self::getServiceCategoryId($order->expedition['service'] ?? '', $serviceCategories),
            'weight' => $order->items->sum(function ($item) {
                return $item->weight * $item->quantity;
            }),
            'total_value' => (float) $order->grand_total,
            'items' => $items,
        ];

        Log::info('AirwaybillService: Getting rates with payload:', $payload);

        return $this->jubelioShipment->getRates($payload);
    }

    /**
     * Map expedition service to Jubelio service category ID.
     *
     * @param string $service
     * @param array|null $serviceCategories
     * @return string
     */
    private static function getServiceCategoryId(string $service, ?array $serviceCategories): string
    {
        $service = strtoupper($service);
        $regulerServices = ['STD', 'EZ', 'IDLITE'];

        $categoryName = null;
        if (in_array($service, $regulerServices)) {
            $categoryName = 'REGULER';
        }
        // Add other mappings here, e.g., for CARGO, NEXTDAY, etc.

        if ($categoryName && !empty($serviceCategories['categories'])) {
            foreach ($serviceCategories['categories'] as $category) {
                if (strtoupper($category['name']) === $categoryName) {
                    return (string) $category['service_category_id'];
                }
            }
        }

        return '1'; // Default to REGULER if no match is found
    }
    /**
     * Get the courier service name based on the expedition service.
     *
     * @param string $service
     * @return string|null
     */
    private function getCourierServiceName(string $service): ?string
    {
        $service = strtoupper($service);
        $serviceMapping = [
            'IDLITE' => 'ID Express Lite',
            'STD' => 'ID Express Standard',
            'EZ' => 'J&T EZ',
        ];

        return $serviceMapping[$service] ?? null;
    }

    /**
     * Find the correct rate based on the expedition service.
     *
     * @param array $rates
     * @param array $rates
     * @param Order $order
     * @return array|null
     */
    private function getRateByExpeditionService(array $rates, Order $order): ?array
    {
        $expeditionService = $order->expedition['service'] ?? '';
        $expeditionCost = (float) ($order->expedition['cost'] ?? 0);

        $courierServiceName = $this->getCourierServiceName($expeditionService);
        if (!$courierServiceName) {
            Log::error("AirwaybillService: Could not find a matching courier service name for expedition service: {$expeditionService}");
            return null;
        }

        foreach ($rates as $rate) {
            if ($rate['courier_service_name'] === $courierServiceName) {
                if ((float) $rate['final_rates'] !== $expeditionCost) {
                    Log::error("AirwaybillService: Price mismatch for {$courierServiceName}. Jubelio final_rates: {$rate['final_rates']}, Order expedition_cost: {$expeditionCost}");
                    return null;
                }
                return $rate;
            }
        }

        Log::warning("AirwaybillService: No rate found for courier service: {$courierServiceName}");
        return null;
    }
    /**
     * Create a shipment in Jubelio.
     *
     * @param Order $order
     * @param array|null $rates
     * @return array|null
     */
    private function createShipment(Order $order, ?array $rates, ?array $originDetails, ?array $destinationDetails, array $categories, array $volumes): ?array
    {
        if (empty($originDetails[0]) || empty($destinationDetails[0])) {
            Log::error('AirwaybillService: Missing origin or destination details for createShipment.');
            return null;
        }

        $origin = $originDetails[0];
        $destination = $destinationDetails[0];

        $items = [];
        $volume = $volumes[0] ?? ['length' => 10, 'width' => 15, 'height' => 5]; // Default volume
        $category = $categories[0] ?? ['label' => 'Paket', 'value' => 'Paket']; // Default category

        foreach ($order->items as $item) {
            $itemName = collect($item->variant)->pluck('value')->implode(', ');
            if (empty($itemName)) {
                $itemName = $item->name;
            }

            $items[] = [
                'item_name' => $itemName,
                'value' => (float) $item->price,
                'quantity' => (int) $item->quantity,
                'length' => $volume['length'],
                'width' => $volume['width'],
                'height' => $volume['height'],
                'weight' => (int) $item->weight,
                'category' => $category['value'],
            ];
        }

        $selectedRate = $this->getRateByExpeditionService($rates, $order);

        if (!$selectedRate) {
            Log::error("AirwaybillService: Could not find a matching and valid rate for order #{$order->id}.");
            return null;
        }

        $payload = [
            'ref_no' => "[auto]",
            'courier_id' => $selectedRate['courier_id'],
            'courier_service_id' => $selectedRate['courier_service_id'],
            'shipping_insurance' => 0,
            'origin' => [
                'name' => config('jne.shipper_name'),
                'phone' => Formatter::normalizePhoneNumber(config('jne.shipper_phone')),
                'address' => config('jne.shipper_address'),
                'note' => '',
                'q_address' => '',
                'area_id' => $origin['area_id'],
                'zipcode' => $origin['zipcode'],
                'coordinate' => null,
                'email' => config('jne.email'),
            ],
            'destination' => [
                'name' => $order->shipping_full_name,
                'phone' => Formatter::normalizePhoneNumber($order->shipping_phone),
                'address' => $order->shipping_address,
                'note' => '',
                'q_address' => '',
                'area_id' => $destination['area_id'],
                'zipcode' => $destination['zipcode'],
                'coordinate' => null,
                'email' => '',
            ],
            'is_cod' => false,
            'is_reserved_cod' => false,
            'items' => $items,
            'promotion_usage_id' => null,
        ];

        Log::info("AirwaybillService: Creating shipment for order #{$order->id} with payload:", $payload);
        // disable for testing (do not active the createShipment yet)
        return $this->jubelioShipment->createShipment($payload);
        // return [];
    }
    private function updateOrderWithTrackingNumber(Order $order, array $shipmentResult): void
    {
        try {
            $trackingNumber = $shipmentResult['awb'];
            $currentSalesOrder = $this->jubelio->getSalesOrderDetail($order->jubelio_order_id);

            if ($currentSalesOrder) {
                $updatePayload = JubelioPayloadBuilder::buildJubelioUpdatePayload($currentSalesOrder, $trackingNumber);
                $this->jubelio->createOrUpdateSalesOrder($updatePayload);

                $etc = $order->etc ?? [];
                $etc['jubelio_shipment'] = $shipmentResult;

                $order->update([
                    'tracking_number' => $trackingNumber,
                    'etc' => $etc,
                ]);

                Log::info("Successfully updated order #{$order->id} with tracking number {$trackingNumber} and shipment details.");
            } else {
                Log::error("Could not find Jubelio sales order for order #{$order->id}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to update order #{$order->id} with tracking number: " . $e->getMessage());
        }
    }

}
