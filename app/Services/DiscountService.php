<?php

namespace App\Services;

use App\Models\MembershipLevel;
use App\Models\ProductDiscount;
use Illuminate\Support\Collection;

class DiscountService
{
    /**
     * Calculate the discounts for a set of items and return the calculated totals.
     *
     * @param array $items
     * @param MembershipLevel|null $membershipLevel
     * @return array
     */
    public function calculateOrderTotals(array $items, ?MembershipLevel $membershipLevel = null): array
    {
        $itemIds = collect($items)->pluck('jubelio_item_group_id');
        $productDiscounts = ProductDiscount::whereIn('jubelio_item_group_id', $itemIds)
            ->pluck('max_discount_percentage', 'jubelio_item_group_id');

        $sub_total = 0;
        $total_discount = 0;
        $processedItems = [];

        foreach ($items as $item) {
            $original_price = (float) $item['original_price'];
            $quantity = (int) $item['quantity'];

            $membership_discount = $membershipLevel ? (float) $membershipLevel->discount_percentage : 0;
            $max_product_discount = (float) $productDiscounts->get($item['jubelio_item_group_id'], config('zafrada.discount_percentage'));

            $final_discount_percentage = min($membership_discount, $max_product_discount);
            $discounted_price = $original_price * (1 - ($final_discount_percentage / 100));

            $sub_total += $original_price * $quantity;
            $total_discount += ($original_price - $discounted_price) * $quantity;

            $processedItem = $item;
            $processedItem['price'] = $discounted_price;
            $processedItem['discount_percentage'] = $final_discount_percentage;
            $processedItems[] = $processedItem;
        }

        return [
            'items' => $processedItems,
            'sub_total' => $sub_total,
            'total_discount' => $total_discount,
        ];
    }
}
