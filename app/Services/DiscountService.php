<?php

namespace App\Services;

use App\Models\MembershipLevel;
use App\Models\FixedPrice;
use App\Models\ProductDiscount;
use Illuminate\Support\Collection;

class DiscountService
{
    /**
     * Calculate the discounts for a set of items and return the calculated totals.
     *
     * @param array $items
     * @param MembershipLevel|null $membershipLevel
     * @return array
     */
    public function calculateOrderTotals(array $items, ?MembershipLevel $membershipLevel = null): array
    {
        $itemGroupIds = collect($items)->pluck('jubelio_item_group_id');
        $itemIds = collect($items)->pluck('jubelio_item_id');

        // Fetch all relevant discounts and fixed prices in advance to minimize queries
        $productDiscounts = ProductDiscount::whereIn('jubelio_item_group_id', $itemGroupIds)
            ->pluck('max_discount_percentage', 'jubelio_item_group_id');

        $fixedPrices = FixedPrice::whereIn('jubelio_item_id', $itemIds)
            ->pluck('price', 'jubelio_item_id');

        $sub_total = 0;
        $total_discount = 0;
        $processedItems = [];

        foreach ($items as $item) {
            $original_price = (float) $item['original_price'];
            $quantity = (int) $item['quantity'];
            $itemId = $item['jubelio_item_id'];
            $itemGroupId = $item['jubelio_item_group_id'];

            $final_discount_percentage = 0;
            $final_price = 0;

            // Check for a fixed price first. This has the highest priority.
            if ($fixedPrices->has($itemId)) {
                $final_price = (float) $fixedPrices->get($itemId);
                $final_discount_percentage = 0; // No discount is applied for fixed prices
            } else {
                // If no fixed price, proceed with the standard discount logic
                $membership_discount = $membershipLevel ? (float) $membershipLevel->discount_percentage : 0;
                $max_product_discount = (float) $productDiscounts->get($itemGroupId, config('zafrada.discount_percentage'));

                $final_discount_percentage = min($membership_discount, $max_product_discount);
                $final_price = $original_price * (1 - ($final_discount_percentage / 100));
            }

            $sub_total += $original_price * $quantity;
            $total_discount += ($original_price - $final_price) * $quantity;

            $processedItem = $item;
            $processedItem['price'] = $final_price;
            $processedItem['discount_percentage'] = $final_discount_percentage;
            $processedItem['original_price'] = (float) $item['original_price'];
            $processedItems[] = $processedItem;
        }

        return [
            'items' => $processedItems,
            'sub_total' => $sub_total,
            'total_discount' => $total_discount,
        ];
    }
}
