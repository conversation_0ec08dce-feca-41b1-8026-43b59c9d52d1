<?php

namespace App\Services;

use App\Utils\Formatter;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WahaService
{
    protected string $baseUrl;
    protected string $apiKey;
    protected string $session;

    /**
     * WahaService constructor.
     */
    public function __construct()
    {
        $this->baseUrl = config('services.waha.base_url');
        $this->apiKey = config('services.waha.api_key');
        $this->session = config('services.waha.session');
    }

    /**
     * Send a text message to a WhatsApp number.
     *
     * @param string $recipient The recipient's phone number (e.g., '6281234567890').
     * @param string $message The text message to send.
     * @return array|bool The response from the API or false on failure.
     */
    public function sendTextMessage(string $recipient, string $message): array|bool
    {
        if (empty($this->baseUrl) || empty($this->apiKey)) {
            Log::error('WAHA service is not configured. Please check your .env file.');
            return false;
        }

        $endpoint = "{$this->baseUrl}/api/sendText";
        $chatId = $this->formatRecipient($recipient);

        Log::info('Sending WhatsApp message via WAHA.', [
            'recipient' => $recipient,
            'chatId' => $chatId,
            // 'message' => $message,
        ]);

        $response = Http::withHeaders([
            'X-Api-Key' => $this->apiKey,
        ])->post($endpoint, [
            'chatId' => $chatId,
            'text' => $message,
            'session' => $this->session,
        ]);

        if ($response->failed()) {
            Log::error('Failed to send WhatsApp message via WAHA.', [
                'status' => $response->status(),
                'response' => $response->json(),
                'recipient' => $recipient,
            ]);
            return false;
        }

        return $response->json();
    }

    /**
     * Formats the recipient's phone number into the required chatId format.
     *
     * @param string $recipient
     * @return string
     */
    private function formatRecipient(string $recipient): string
    {
        $recipient = Formatter::normalizeRecipient($recipient);

        return $recipient . '@c.us';
    }
}
