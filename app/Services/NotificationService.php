<?php

namespace App\Services;

use App\Models\User;
use App\Models\NotificationType;
use App\Notifications\BaseNotification; // Import the new base class
use Illuminate\Notifications\Notification;

class NotificationService
{
    /**
     * Send a notification to a user based on their settings.
     *
     * @param User $user
     * @param BaseNotification $notification
     * @param string $notificationTypeSlug
     * @return void
     */
    public function send(User $user, BaseNotification $notification, string $notificationTypeSlug): void
    {
        // Step 1: Always send the in-app (database) notification.
        // We clone the notification to avoid modifying the original object.
        $databaseNotification = clone $notification;
        $databaseNotification->setChannel('database');
        $user->notifyNow($databaseNotification);


        // Step 2: Determine the additional channel based on user settings or default.
        $notificationType = NotificationType::where('slug', $notificationTypeSlug)->first();
        if (!$notificationType) {
            return; // Or log an error if the type is not found
        }

        $setting = $user->userNotificationSettings()
            ->where('notification_type_id', $notificationType->id)
            ->first();

        $channelSlug = 'mail'; // Default channel

        if ($setting) {
            // If a setting exists, use the user's chosen channel
            $channelSlug = $setting->channel->slug;
        }

        // Step 3: If the channel is not 'off', send the notification via that channel.
        if ($channelSlug !== 'off') {
            $additionalNotification = clone $notification;
            $additionalNotification->setChannel($channelSlug);
            $user->notifyNow($additionalNotification);
        }
    }

    /**
     * Send an in-app notification only, ignoring user settings.
     *
     * @param User $user
     * @param BaseNotification $notification
     * @return void
     */
    public function sendInAppOnly(User $user, BaseNotification $notification): void
    {
        $databaseNotification = clone $notification;
        $databaseNotification->setChannel('database');
        $user->notifyNow($databaseNotification);
    }
}
