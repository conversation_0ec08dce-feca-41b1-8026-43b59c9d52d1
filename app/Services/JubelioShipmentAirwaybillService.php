<?php

namespace App\Services;

use App\Models\Order;
use App\Utils\Formatter;
use App\Utils\JubelioPayloadBuilder;
use Illuminate\Support\Facades\Log;

class JubelioShipmentAirwaybillService
{
    protected JubelioShipment $jubelioShipment;
    protected Jubelio $jubelio;
    protected string $originSubDistrict;
    protected string $originDistrict;
    protected string $originCity;
    protected string $originProvince;

    public function __construct(JubelioShipment $jubelioShipment, Jubelio $jubelio)
    {
        $this->jubelioShipment = $jubelioShipment;
        $this->jubelio = $jubelio;
        $this->originSubDistrict = config('jne.shipper_sub_district');
        $this->originDistrict = config('jne.shipper_district');
        $this->originCity = config('jne.shipper_city');
        $this->originProvince = config('jne.shipper_province');
    }

    /**
     * Handle the creation of an airwaybill for an expedition order.
     *
     * @param Order $order
     * @return array
     */
    public function handle(Order $order): array
    {
        try {
            Log::info("Handling jubelio shipment for order #{$order->id}");

            $originDetails = $this->getOriginDetails();
            $destinationDetails = $this->getDestinationDetails($order);

            $result = $this->createShipment($order, $originDetails, $destinationDetails);
            Log::info('Shipment Result:', (array) $result);

            if ($result && isset($result['awb'])) {
                $this->updateOrderWithTrackingNumber($order, $result);
                return ['success' => true, 'message' => 'Jubelio Shipment airwaybill generated successfully.'];
            }

            $errorMessage = $result['message'] ?? 'Failed to generate Jubelio Shipment airwaybill.';
            return ['success' => false, 'message' => $errorMessage];
        } catch (\Exception $e) {
            Log::error("Error handling expedition for order #{$order->id}: " . $e->getMessage(), [
                'exception' => $e,
            ]);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Get and log the origin details from Jubelio.
     *
     * @return array|null
     */
    private function getOriginDetails(): ?array
    {
        $originName = "{$this->originSubDistrict}, {$this->originDistrict}, {$this->originCity}, {$this->originProvince}";
        $originDetails = $this->jubelioShipment->getRegions($originName);
        Log::info('Origin Details:', ['name' => $originName, 'details' => $originDetails]);

        return $originDetails;
    }

    /**
     * Get and log the destination details from Jubelio based on the order.
     *
     * @param Order $order
     * @return array|null
     */
    private function getDestinationDetails(Order $order): ?array
    {
        $destinationName = "{$order->shipping_subdistrict}, {$order->shipping_district}, {$order->shipping_city}, {$order->shipping_province}";
        $destinationDetails = $this->jubelioShipment->getRegions($destinationName);
        Log::info('Destination Details:', ['name' => $destinationName, 'details' => $destinationDetails]);

        return $destinationDetails;
    }

    /**
     * Create a shipment in Jubelio.
     *
     * @param Order $order
     * @param array|null $originDetails
     * @param array|null $destinationDetails
     * @return array|null
     */
    private function createShipment(Order $order, ?array $originDetails, ?array $destinationDetails): ?array
    {
        if (empty($originDetails[0]) || empty($destinationDetails[0])) {
            Log::error('JubelioShipmentAirwaybillService: Missing origin or destination details for createShipment.');
            return null;
        }

        $origin = $originDetails[0];
        $destination = $destinationDetails[0];

        $items = [];
        $volume = JubelioShipment::defaultVolume();
        $category = JubelioShipment::defaultCategory();

        foreach ($order->items as $item) {
            $itemName = collect($item->variant)->pluck('value')->implode(', ');
            if (empty($itemName)) {
                $itemName = $item->name;
            }

            $items[] = [
                'item_name' => $itemName,
                'value' => (float) $item->price,
                'quantity' => (int) $item->quantity,
                'length' => $volume['length'],
                'width' => $volume['width'],
                'height' => $volume['height'],
                'weight' => (int) $item->weight,
                'category' => $category['value'],
            ];
        }

        $salesOrderDetail = $this->jubelio->getSalesOrderDetail($order->jubelio_order_id);
        $refNo = $salesOrderDetail['salesorder_no'] ?? '[auto]';

        $payload = [
            'ref_no' => $refNo,
            'courier_id' => $order->expedition['courier_id'],
            'courier_service_id' => $order->expedition['courier_service_id'],
            'shipping_insurance' => 0,
            'origin' => [
                'name' => config('jne.shipper_name'),
                'phone' => Formatter::normalizePhoneNumber(config('jne.shipper_phone')),
                'address' => config('jne.shipper_address'),
                'note' => '',
                'q_address' => '',
                'area_id' => $origin['area_id'],
                'zipcode' => $origin['zipcode'],
                'coordinate' => null,
                'email' => config('jne.email'),
            ],
            'destination' => [
                'name' => $order->shipping_full_name,
                'phone' => Formatter::normalizePhoneNumber($order->shipping_phone),
                'address' => $order->shipping_address,
                'note' => '',
                'q_address' => '',
                'area_id' => $destination['area_id'],
                'zipcode' => $destination['zipcode'],
                'coordinate' => null,
                'email' => '',
            ],
            'is_cod' => false,
            'is_reserved_cod' => false,
            'items' => $items,
            'promotion_usage_id' => null,
        ];

        Log::info("JubelioShipmentAirwaybillService: Creating shipment for order #{$order->id} with payload:", $payload);
        return $this->jubelioShipment->createShipment($payload);
    }

    private function updateOrderWithTrackingNumber(Order $order, array $shipmentResult): void
    {
        try {
            $trackingNumber = $shipmentResult['awb'];
            $currentSalesOrder = $this->jubelio->getSalesOrderDetail($order->jubelio_order_id);

            if ($currentSalesOrder) {
                $updatePayload = JubelioPayloadBuilder::buildJubelioUpdatePayload($currentSalesOrder, $trackingNumber);
                $this->jubelio->createOrUpdateSalesOrder($updatePayload);

                $etc = $order->etc ?? [];
                $etc['jubelio_shipment'] = $shipmentResult;

                $order->update([
                    'tracking_number' => $trackingNumber,
                    'etc' => $etc,
                ]);

                Log::info("Successfully updated order #{$order->id} with tracking number {$trackingNumber} and shipment details.");
            } else {
                Log::error("Could not find Jubelio sales order for order #{$order->id}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to update order #{$order->id} with tracking number: " . $e->getMessage());
        }
    }
}
