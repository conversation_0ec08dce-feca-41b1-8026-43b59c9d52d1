<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Builder;
use <PERSON><PERSON>\SimpleExcel\SimpleExcelWriter;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Closure;

class CsvExportService
{
    /**
     * @param Builder $query The Eloquent query to execute.
     * @param string $filename The desired filename for the download.
     * @param array $headings The CSV header row.
     * @param Closure $formatter A function that takes a model instance and returns a formatted array for the CSV row.
     * @return StreamedResponse
     */
    public function streamCollection(Builder $query, string $filename, array $headings, Closure $formatter): StreamedResponse
    {
        return response()->streamDownload(function () use ($query, $headings, $formatter) {
            $writer = SimpleExcelWriter::create('php://output')
                ->addHeader($headings);

            $query->chunk(500, function ($chunk) use ($writer, $formatter) {
                $formattedRows = $chunk->map($formatter)->toArray();
                $writer->addRows($formattedRows);
            });
        }, $filename);
    }
}
