<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class Jnt
{
    protected $baseUrl;
    protected $apiKey;
    protected $eccompanyId;
    protected $customerName;
    protected $password;

    public function __construct()
    {
        $this->baseUrl = config('jnt.base_url');
        $this->apiKey = config('jnt.api_key');
        $this->eccompanyId = config('jnt.eccompany_id');
        $this->customerName = config('jnt.customer_name');
        $this->password = config('jnt.password');
    }

    /**
     * Get a pre-configured HTTP client instance for JNT.
     *
     * @return \Illuminate\Http\Client\PendingRequest
     */
    private function client()
    {
        return Http::baseUrl($this->baseUrl)
            ->withHeaders([
                'Content-Type' => 'application/x-www-form-urlencoded',
                'Accept' => 'application/json',
                'User-Agent' => 'OtoAPI',
            ]);
    }

    /**
     * Send a request to the JNT API.
     *
     * @param string $method
     * @param string $endpoint
     * @param array $payload
     * @return array|null
     */
    private function sendRequest(string $method, string $endpoint, array $payload): ?array
    {
        try {
            $response = $this->client()->asForm()->{$method}($endpoint, $payload);

            if ($response->failed()) {
                Log::error("Failed to call JNT API: {$endpoint}", [
                    'request' => $payload,
                    'response_status' => $response->status(),
                    'response_body' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info("Successfully called JNT API: {$endpoint}", [
                'request' => $payload,
                'response' => $responseData,
            ]);

            return $responseData;
        } catch (\Exception $e) {
            Log::error("Exception when calling JNT API: {$endpoint}", [
                'request' => $payload,
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Generate a signature for the request.
     *
     * @param string $data
     * @return string
     */
    private function generateSign(string $data): string
    {
        return base64_encode(md5($data . $this->apiKey));
    }

    /**
     * Create an Air Waybill (AWB).
     *
     * @param array $payload
     *  $payload = [
     *      'orderid' => (string) The order ID.
     *      'shipper_name' => (string) The shipper's name.
     *      'shipper_contact' => (string) The shipper's contact name.
     *      'shipper_phone' => (string) The shipper's phone number.
     *      'shipper_addr' => (string) The shipper's address.
     *      'origin_code' => (string) The origin code.
     *      'receiver_name' => (string) The receiver's name.
     *      'receiver_phone' => (string) The receiver's phone number.
     *      'receiver_addr' => (string) The receiver's address.
     *      'receiver_zip' => (string) The receiver's zip code.
     *      'destination_code' => (string) The destination code.
     *      'receiver_area' => (string) The receiver's area.
     *      'qty' => (string) The quantity of items.
     *      'weight' => (string) The weight of the shipment.
     *      'goodsdesc' => (string) The description of the goods.
     *      'servicetype' => (string) The service type.
     *      'insurance' => (string) The insurance value.
     *      'orderdate' => (string) The order date in 'Y-m-d H:i:s' format.
     *      'item_name' => (string) The name of the item.
     *      'cod' => (string) The Cash on Delivery amount.
     *      'sendstarttime' => (string) The send start time in 'Y-m-d H:i:s' format.
     *      'sendendtime' => (string) The send end time in 'Y-m-d H:i:s' format.
     *      'expresstype' => (string) The express type.
     *      'goodsvalue' => (string) The value of the goods.
     *  ];
     * @return array|null
     */
    public function createAwb(array $payload = []): ?array
    {
        $data = array_merge([
            'username' => $this->customerName,
            'api_key' => $this->apiKey,
        ], $payload);

        $data_json = json_encode(['detail' => [$data]]);
        $data_sign = $this->generateSign($data_json);

        $requestPayload = [
            'data_param' => $data_json,
            'data_sign' => $data_sign,
        ];

        return $this->sendRequest('POST', '/jnt-awb', $requestPayload);
    }

    /**
     * Track an Air Waybill (AWB).
     *
     * @param string $awb
     * @return array|null
     */
    public function trackAwb(string $awb): ?array
    {
        $payload = [
            'awb' => $awb,
            'eccompanyid' => $this->eccompanyId,
        ];

        $client = Http::baseUrl($this->baseUrl)
            ->withBasicAuth($this->customerName, $this->password)
            ->withHeaders([
                'Content-Type' => 'text/plain',
                'Accept' => 'application/json',
                'User-Agent' => 'OtoAPI',
            ]);

        try {
            $response = $client->post('/jnt-tracking', $payload);

            if ($response->failed()) {
                Log::error("Failed to call JNT API: /jnt-tracking", [
                    'request' => $payload,
                    'response_status' => $response->status(),
                    'response_body' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info("Successfully called JNT API: /jnt-tracking", [
                'request' => $payload,
                'response' => $responseData,
            ]);

            return $responseData;
        } catch (\Exception $e) {
            Log::error("Exception when calling JNT API: /jnt-tracking", [
                'request' => $payload,
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Check Tariff.
     *
     * @param array $payload
     *  $payload = [
     *      'weight' => (string) The weight of the shipment.
     *      'sendSiteCode' => (string) The sending site code.
     *      'destAreaCode' => (string) The destination area code.
     *      'productType' => (string) The product type (e.g., 'EZ').
     *  ];
     * @return array|null
     */
    public function checkTariff(array $payload = []): ?array
    {
        $data = array_merge([
            'cusName' => $this->customerName,
        ], $payload);

        $data_json = json_encode($data);
        $data_sign = $this->generateSign($data_json);

        $requestPayload = [
            'data' => $data_json,
            'sign' => $data_sign,
        ];

        return $this->sendRequest('POST', '/jnt-tariff', $requestPayload);
    }

    /**
     * Cancel an Air Waybill (AWB).
     *
     * @param array $payload
     *  $payload = [
     *      'orderid' => (string) The order ID to cancel.
     *      'remark' => (string) The reason for cancellation.
     *  ];
     * @return array|null
     */
    public function cancelAwb(array $payload = []): ?array
    {
        $data = array_merge([
            'username' => $this->customerName,
            'api_key' => $this->apiKey,
        ], $payload);

        $data_param = json_encode(['detail' => [$data]]);
        $data_sign = $this->generateSign($data_param);

        $requestPayload = [
            'data_param' => $data_param,
            'data_sign' => $data_sign,
        ];

        return $this->sendRequest('POST', '/jnt-awb-cancel', $requestPayload);
    }
}
