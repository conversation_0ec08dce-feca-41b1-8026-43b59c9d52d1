<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class Jne
{
    protected $base_url;
    protected $username;
    protected $api_key;

    public function __construct()
    {
        $this->base_url = config('jne.base_url');
        $this->username = config('jne.username');
        $this->api_key = config('jne.api_key');
    }

    /**
     * Get a pre-configured HTTP client instance for JNE.
     *
     * @return \Illuminate\Http\Client\PendingRequest
     */
    private function client()
    {
        return Http::baseUrl($this->base_url)
            ->asForm()
            ->withHeaders([
                'Content-Type' => 'application/x-www-form-urlencoded',
                'Accept' => 'application/json',
            ]);
    }

    /**
     * Get pricing information.
     *
     * @param array $data
     * @return array|null
     */
    public function getPrices(array $data): ?array
    {
        $payload = $this->buildPricesPayload($data);

        return $this->sendRequest('POST', '/tracing/api/pricedev', $payload);
    }

    /**
     * Build the payload for getting prices.
     *
     * @param array $data
     * @return array
     */
    private function buildPricesPayload(array $data): array
    {
        return array_merge([
            'username' => $this->username,
            'api_key' => $this->api_key,
            'from' => '',
            'thru' => '',
            'weight' => 1, // on Kg
        ], $data);
    }

    /**
     * Generate an airway bill (cnote).
     *
     * @param array $data
     * @return array|null
     */
    public function generateAirwaybill(array $data): ?array
    {
        $payload = $this->buildAirwaybillPayload($data);

        return $this->sendRequest('POST', '/tracing/api/generatecnote', $payload);
    }

    /**
     * Build the payload for generating an airway bill.
     *
     * @param array $data
     * @return array
     */
    private function buildAirwaybillPayload(array $data): array
    {
        return array_merge([
            'username' => $this->username,
            'api_key' => $this->api_key,
            'OLSHOP_BRANCH' => '',
            'OLSHOP_CUST' => '',
            'OLSHOP_ORDERID' => '',
            'OLSHOP_SHIPPER_NAME' => '',
            'OLSHOP_SHIPPER_ADDR1' => '',
            'OLSHOP_SHIPPER_ADDR2' => '',
            'OLSHOP_SHIPPER_ADDR3' => '',
            'OLSHOP_SHIPPER_CITY' => '',
            'OLSHOP_SHIPPER_REGION' => '',
            'OLSHOP_SHIPPER_ZIP' => '',
            'OLSHOP_SHIPPER_PHONE' => '',
            'OLSHOP_RECEIVER_NAME' => '',
            'OLSHOP_RECEIVER_ADDR1' => '',
            'OLSHOP_RECEIVER_ADDR2' => '',
            'OLSHOP_RECEIVER_ADDR3' => '',
            'OLSHOP_RECEIVER_CITY' => '',
            'OLSHOP_RECEIVER_REGION' => '',
            'OLSHOP_RECEIVER_ZIP' => '',
            'OLSHOP_RECEIVER_PHONE' => '',
            'OLSHOP_QTY' => 1,
            'OLSHOP_WEIGHT' => 1, // on Kg
            'OLSHOP_GOODSDESC' => '',
            'OLSHOP_GOODSVALUE' => 0,
            'OLSHOP_GOODSTYPE' => 2,
            'OLSHOP_INST' => '',
            'OLSHOP_INS_FLAG' => 'N',
            'OLSHOP_ORIG' => '',
            'OLSHOP_DEST' => '',
            'OLSHOP_SERVICE' => '',
            'OLSHOP_COD_FLAG' => 'N',
            'OLSHOP_COD_AMOUNT' => 0,
        ], $data);
    }

    /**
     * Track an airway bill (cnote).
     *
     * @param string $airwaybill
     * @return array|null
     */
    public function trackAirwaybill(string $airwaybill): ?array
    {
        $payload = [
            'username' => $this->username,
            'api_key' => $this->api_key,
        ];

        return $this->sendRequest('POST', "/tracing/api/list/v1/cnote/{$airwaybill}", $payload);
    }

    /**
     * Send a request to the JNE API.
     *
     * @param string $method
     * @param string $endpoint
     * @param array $payload
     * @return array|null
     */
    private function sendRequest(string $method, string $endpoint, array $payload): ?array
    {
        try {
            $response = $this->client()->{$method}($endpoint, $payload);

            if ($response->failed()) {
                Log::error("Failed to call JNE API: {$endpoint}", [
                    'request' => $payload,
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info("Successfully called JNE API: {$endpoint}", [
                'request' => $payload,
                'response' => $responseData,
            ]);

            return $responseData;
        } catch (\Exception $e) {
            Log::error("Exception when calling JNE API: {$endpoint}", [
                'request' => $payload,
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }
}
