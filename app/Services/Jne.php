<?php

namespace App\Services;

use App\Models\JneBranch;
use Guz<PERSON><PERSON>ttp\Client;
use Guz<PERSON>Http\Cookie\CookieJar;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class Jne
{
    protected $username;
    protected $api_key;
    protected $base_url_pricing;
    protected $base_url_cnote;
    protected $base_url_tracking;

    public function __construct()
    {
        $this->username = config('jne.username');
        $this->api_key = config('jne.api_key');
        $this->base_url_pricing = config('jne.base_url_pricing');
        $this->base_url_cnote = config('jne.base_url_cnote');
        $this->base_url_tracking = config('jne.base_url_tracking');
    }

    /**
     * Get a pre-configured HTTP client instance for JNE.
     *
     * @return \Illuminate\Http\Client\PendingRequest
     */
    private function client($baseUrl)
    {
        $cookieJar = new CookieJar();
        return Http::baseUrl($baseUrl)
            ->withOptions(['cookies' => $cookieJar])
            ->withHeaders([
                'Content-Type' => 'application/x-www-form-urlencoded',
                'Accept' => 'application/json',
                'User-Agent' => 'OtoAPI',
            ]);
    }

    /**
     * Get a pre-configured Guzzle HTTP client instance for JNE.
     *
     * @return \GuzzleHttp\Client
     */
    private function guzzleClient($baseUrl)
    {
        return new Client([
            'base_uri' => $baseUrl,
            // 'cookies' => true,
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
                'Accept' => 'application/json',
                // 'User-Agent' => 'Postman',
            ]
        ]);
    }

    /**
     * Get pricing information.
     *
     * @param array $data
     * @return array|null
     */
    public function getPrices(array $data): ?array
    {
        $payload = $this->buildPricesPayload($data);

        return $this->sendRequest('POST', '/tracing/api/pricedev', $payload, $this->client($this->base_url_pricing));
    }

    public function getPricesWithGuzzle(array $data): ?array
    {
        $payload = $this->buildPricesPayload($data);

        return $this->sendGuzzleRequest('POST', '/tracing/api/pricedev', $payload, $this->guzzleClient($this->base_url_pricing));
    }

    public function getPricesWithCurl(array $data): ?array
    {
        $payload = $this->buildPricesPayload($data);

        return $this->sendCurlRequest('POST', $this->base_url_pricing . '/tracing/api/pricedev', $payload);
    }

    /**
     * Build the payload for getting prices.
     *
     * @param array $data
     * @return array
     */
    private function buildPricesPayload(array $data): array
    {
        return array_merge([
            'username' => $this->username,
            'api_key' => $this->api_key,
            'from' => '',
            'thru' => '',
            'weight' => 1, // on Kg
        ], $data);
    }

    /**
     * Generate an airway bill (cnote).
     *
     * @param array $data
     * @return array|null
     */
    public function generateAirwaybill(array $data): ?array
    {
        $payload = $this->buildAirwaybillPayload($data);

        return $this->sendRequest('POST', 'tracing/api/generatecnote', $payload, $this->client($this->base_url_cnote));
    }

    public function generateAirwaybillWithGuzzle(array $data): ?array
    {
        $payload = $this->buildAirwaybillPayload($data);

        return $this->sendGuzzleRequest('POST', 'tracing/api/generatecnote', $payload, $this->guzzleClient($this->base_url_cnote));
    }

    public function generateAirwaybillWithCurl(array $data): ?array
    {
        $payload = $this->buildAirwaybillPayload($data);

        return $this->sendCurlRequest('POST', $this->base_url_cnote . '/tracing/api/generatecnote', $payload);
    }

    /**
     * Build the payload for generating an airway bill.
     *
     * @param array $data
     * @return array
     */
    private function buildAirwaybillPayload(array $data): array
    {
        $defaultBranch = JneBranch::where('is_default', true)->first();

        return array_merge([
            'username' => $this->username,
            'api_key' => $this->api_key,
            'OLSHOP_BRANCH' => $defaultBranch ? $defaultBranch->branch_code : config('jne.branch_code'),
            'OLSHOP_CUST' => config('jne.cust'),
            // 'OLSHOP_ORDERID' => '',
            // 'OLSHOP_SHIPPER_NAME' => '',
            // 'OLSHOP_SHIPPER_ADDR1' => '',
            // 'OLSHOP_SHIPPER_ADDR2' => '',
            // 'OLSHOP_SHIPPER_ADDR3' => '',
            // 'OLSHOP_SHIPPER_CITY' => '',
            // 'OLSHOP_SHIPPER_REGION' => '',
            // 'OLSHOP_SHIPPER_ZIP' => '',
            // 'OLSHOP_SHIPPER_PHONE' => '',
            // 'OLSHOP_RECEIVER_NAME' => '',
            // 'OLSHOP_RECEIVER_ADDR1' => '',
            // 'OLSHOP_RECEIVER_ADDR2' => '',
            // 'OLSHOP_RECEIVER_ADDR3' => '',
            // 'OLSHOP_RECEIVER_CITY' => '',
            // 'OLSHOP_RECEIVER_REGION' => '',
            // 'OLSHOP_RECEIVER_ZIP' => '',
            // 'OLSHOP_RECEIVER_PHONE' => '',
            // 'OLSHOP_QTY' => 1,
            // 'OLSHOP_WEIGHT' => 1, // on Kg
            'OLSHOP_GOODSDESC' => 'Otoresell Products',
            // 'OLSHOP_GOODSVALUE' => 0,
            'OLSHOP_GOODSTYPE' => 2,
            'OLSHOP_INST' => 'Take care the products',
            'OLSHOP_INS_FLAG' => 'N',
            // 'OLSHOP_ORIG' => '',
            // 'OLSHOP_DEST' => '',
            // 'OLSHOP_SERVICE' => '',
            'OLSHOP_COD_FLAG' => 'N',
            'OLSHOP_COD_AMOUNT' => 0,
        ], $data);
    }

    /**
     * Track an airway bill (cnote).
     *
     * @param string $airwaybill
     * @return array|null
     */
    public function trackAirwaybill(string $airwaybill): ?array
    {
        $payload = [
            'username' => $this->username,
            'api_key' => $this->api_key,
        ];

        return $this->sendRequest('POST', "/tracing/api/list/v1/cnote/{$airwaybill}", $payload, $this->client($this->base_url_tracking));
    }

    public function trackAirwaybillWithGuzzle(string $airwaybill): ?array
    {
        $payload = [
            'username' => $this->username,
            'api_key' => $this->api_key,
        ];

        return $this->sendGuzzleRequest('POST', "/tracing/api/list/v1/cnote/{$airwaybill}", $payload, $this->guzzleClient($this->base_url_tracking));
    }

    public function trackAirwaybillWithCurl(string $airwaybill): ?array
    {
        $payload = [
            'username' => $this->username,
            'api_key' => $this->api_key,
        ];

        return $this->sendCurlRequest('POST', $this->base_url_tracking . "/tracing/api/list/v1/cnote/{$airwaybill}", $payload);
    }

    /**
     * Send a request to the JNE API.
     *
     * @param string $method
     * @param string $endpoint
     * @param array $payload
     * @return array|null
     */
    private function sendRequest(string $method, string $endpoint, array $payload, $client): ?array
    {
        try {
            $response = $client->asForm()->{$method}($endpoint, $payload);

            if ($response->failed()) {
                Log::error("Failed to call JNE API: {$endpoint}", [
                    'request' => $payload,
                    'response_status' => $response->status(),
                    'response_body' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info("Successfully called JNE API: {$endpoint}", [
                'request' => $payload,
                'response' => $responseData,
            ]);

            return $responseData;
        } catch (\Exception $e) {
            Log::error("Exception when calling JNE API: {$endpoint}", [
                'request' => $payload,
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Send a request to the JNE API using Guzzle.
     *
     * @param string $method
     * @param string $endpoint
     * @param array $payload
     * @param \GuzzleHttp\Client $client
     * @return array|null
     */
    private function sendGuzzleRequest(string $method, string $endpoint, array $payload, $client): ?array
    {
        try {
            $response = $client->request($method, $endpoint, [
                'form_params' => $payload
            ]);

            if ($response->getStatusCode() >= 400) {
                Log::error("Failed to call JNE API: {$endpoint}", [
                    'request' => $payload,
                    'response_status' => $response->getStatusCode(),
                    'response_body' => $response->getBody()->getContents(),
                ]);
                return null;
            }

            $responseData = json_decode($response->getBody()->getContents(), true);
            Log::info("Successfully called JNE API: {$endpoint}", [
                'request' => $payload,
                'response' => $responseData,
            ]);

            return $responseData;
        } catch (RequestException $e) {
            Log::error("Exception when calling JNE API: {$endpoint}", [
                'request' => $payload,
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Send a request to the JNE API using cURL.
     *
     * @param string $method
     * @param string $url
     * @param array $payload
     * @return array|null
     */
    private function sendCurlRequest(string $method, string $url, array $payload): ?array
    {
        $ch = curl_init();

        $headers = [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json',
            'User-Agent: Postman',
        ];

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($payload));

        try {
            $response = curl_exec($ch);
            $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if ($httpcode >= 400) {
                Log::error("Failed to call JNE API: {$url}", [
                    'request' => $payload,
                    'response_status' => $httpcode,
                    'response_body' => $response,
                ]);
                curl_close($ch);
                return null;
            }

            $responseData = json_decode($response, true);
            Log::info("Successfully called JNE API: {$url}", [
                'request' => $payload,
                'response' => $responseData,
            ]);

            curl_close($ch);
            return $responseData;
        } catch (\Exception $e) {
            Log::error("Exception when calling JNE API: {$url}", [
                'request' => $payload,
                'exception_message' => $e->getMessage(),
            ]);
            curl_close($ch);
            return null;
        }
    }
}
