<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class JubelioShipment
{
    protected $baseUrl;
    protected $jubelioService;

    public function __construct(Jubelio $jubelioService)
    {
        $this->baseUrl = config('jubelio.shipment_base_url');
        $this->jubelioService = $jubelioService;
    }

    /**
     * Exchange the main Jubelio token for a shipment-specific token.
     *
     * @return string|null
     */
    protected function exchangeToken(): ?string
    {
        $jubelioToken = $this->jubelioService->getToken();
        if (!$jubelioToken) {
            Log::error('JubelioShipment: Could not retrieve main Jubelio token for exchange.');
            return null;
        }

        try {
            $response = Http::withToken($jubelioToken)
                ->baseUrl($this->baseUrl)
                ->get('/auth/exchange-token');

            if ($response->failed()) {
                Log::error('JubelioShipment: Token exchange failed.', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $token = $response->json('token');
            Cache::put('jubelio_shipment_token', $token, now()->addHours(11)); // Cache for 11 hours

            return $token;

        } catch (\Exception $e) {
            Log::error('JubelioShipment: Exception during token exchange.', [
                'message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get the shipment token from cache or exchange for a new one.
     *
     * @return string|null
     */
    protected function getToken(): ?string
    {
        return Cache::remember('jubelio_shipment_token', now()->addHours(11), function () {
            return $this->exchangeToken();
        });
    }

    /**
     * Get a pre-configured HTTP client for the Jubelio Shipment API.
     *
     * @return \Illuminate\Http\Client\PendingRequest|null
     */
    protected function client()
    {
        $token = $this->getToken();
        if (!$token) {
            return null;
        }

        return Http::withToken($token)
            ->baseUrl($this->baseUrl)
            ->contentType('application/json')
            ->acceptJson();
    }

    /**
     * Get a list of shipments from Jubelio.
     *
     * @param array $params
     * @return array|null
     */
    public function listShipments(array $params = []): ?array
    {
        $client = $this->client();
        if (!$client) {
            Log::error('JubelioShipment: Failed to initialize API client for listShipments.');
            return null;
        }

        $defaults = [
            'status' => 'ALL',
            'page' => 1,
            'page_size' => 25,
            'sort_by' => 'shipment_date',
            'sort_direction' => 'DESC',
        ];

        $queryParams = array_merge($defaults, $params);
        Log::info('JubelioShipment: Listing shipments with params:', $queryParams);

        try {
            $response = $client->get('/shipments', $queryParams);

            if ($response->failed()) {
                Log::error('JubelioShipment: Failed to list shipments.', [
                    'request' => $queryParams,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('JubelioShipment: Successfully listed shipments.', [
                'response' => $responseData,
            ]);
            return $responseData;

        } catch (\Exception $e) {
            Log::error('JubelioShipment: Exception when listing shipments.', [
                'request' => $queryParams,
                'message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get the details of a specific shipment from Jubelio.
     *
     * @param int $shipment_id
     * @return array|null
     */
    public function getShipmentDetails(int $shipment_id): ?array
    {
        $client = $this->client();
        if (!$client) {
            Log::error('JubelioShipment: Failed to initialize API client for getShipmentDetails.');
            return null;
        }

        Log::info('JubelioShipment: Getting shipment details for shipment_id:', ['shipment_id' => $shipment_id]);

        try {
            $response = $client->get("/shipments/" . $shipment_id);

            if ($response->failed()) {
                Log::error('JubelioShipment: Failed to get shipment details.', [
                    'shipment_id' => $shipment_id,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('JubelioShipment: Successfully retrieved shipment details.', [
                'shipment_id' => $shipment_id,
                'response' => $responseData,
            ]);
            return $responseData;

        } catch (\Exception $e) {
            Log::error('JubelioShipment: Exception when getting shipment details.', [
                'shipment_id' => $shipment_id,
                'message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get the log of a specific shipment from Jubelio.
     *
     * @param int $shipment_id
     * @param array $params
     * @return array|null
     */
    public function getShipmentLog(int $shipment_id, array $params = []): ?array
    {
        $client = $this->client();
        if (!$client) {
            Log::error('JubelioShipment: Failed to initialize API client for getShipmentLog.');
            return null;
        }

        $defaults = [
            'page' => 1,
            'page_size' => 20,
        ];

        $queryParams = array_merge($defaults, $params);
        Log::info('JubelioShipment: Getting shipment log with params:', array_merge(['shipment_id' => $shipment_id], $queryParams));

        try {
            $response = $client->get("/shipments/log/{$shipment_id}", $queryParams);

            if ($response->failed()) {
                Log::error('JubelioShipment: Failed to get shipment log.', [
                    'shipment_id' => $shipment_id,
                    'request' => $queryParams,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('JubelioShipment: Successfully retrieved shipment log.', [
                'shipment_id' => $shipment_id,
                'response' => $responseData,
            ]);
            return $responseData;

        } catch (\Exception $e) {
            Log::error('JubelioShipment: Exception when getting shipment log.', [
                'shipment_id' => $shipment_id,
                'request' => $queryParams,
                'message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get a list of service categories from Jubelio.
     *
     * @return array|null
     */
    public function getServiceCategories(): ?array
    {
        $client = $this->client();
        if (!$client) {
            Log::error('JubelioShipment: Failed to initialize API client for getServiceCategories.');
            return null;
        }

        try {
            $response = $client->get('/services/categories');

            if ($response->failed()) {
                Log::error('JubelioShipment: Failed to get service categories.', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('JubelioShipment: Successfully retrieved service categories.', [
                'response' => $responseData,
            ]);
            return $responseData;

        } catch (\Exception $e) {
            Log::error('JubelioShipment: Exception when getting service categories.', [
                'message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get a list of shipment cancellation reasons from Jubelio.
     *
     * @return array|null
     */
    public function getShipmentCancelReasons(): ?array
    {
        $client = $this->client();
        if (!$client) {
            Log::error('JubelioShipment: Failed to initialize API client for getShipmentCancelReasons.');
            return null;
        }

        try {
            $response = $client->get('/shipments/cancel/reason');

            if ($response->failed()) {
                Log::error('JubelioShipment: Failed to get shipment cancellation reasons.', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('JubelioShipment: Successfully retrieved shipment cancellation reasons.', [
                'response' => $responseData,
            ]);
            return $responseData;

        } catch (\Exception $e) {
            Log::error('JubelioShipment: Exception when getting shipment cancellation reasons.', [
                'message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get a list of addresses from Jubelio.
     *
     * @param array $params
     * @return array|null
     */
    public function getAddressList(array $params = []): ?array
    {
        $client = $this->client();
        if (!$client) {
            Log::error('JubelioShipment: Failed to initialize API client for getAddressList.');
            return null;
        }

        $defaults = [
            'q' => '',
            'page' => 1,
            'page_size' => 3,
        ];

        $queryParams = array_merge($defaults, $params);

        try {
            $response = $client->get('/setting/address/list', $queryParams);

            if ($response->failed()) {
                Log::error('JubelioShipment: Failed to get address list.', [
                    'request' => $queryParams,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('JubelioShipment: Successfully retrieved address list.', [
                'response' => $responseData,
            ]);
            return $responseData;

        } catch (\Exception $e) {
            Log::error('JubelioShipment: Exception when getting address list.', [
                'request' => $queryParams,
                'message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get a list of regions from Jubelio.
     *
     * @param string $name
     * @return array|null
     */
    public function getRegions(string $name): ?array
    {
        $client = $this->client();
        if (!$client) {
            Log::error('JubelioShipment: Failed to initialize API client for getRegions.');
            return null;
        }

        $queryParams = ['name' => $name];

        try {
            $response = $client->get('/regions', $queryParams);

            if ($response->failed()) {
                Log::error('JubelioShipment: Failed to get regions.', [
                    'request' => $queryParams,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('JubelioShipment: Successfully retrieved regions.', [
                'response' => $responseData,
            ]);
            return $responseData;

        } catch (\Exception $e) {
            Log::error('JubelioShipment: Exception when getting regions.', [
                'request' => $queryParams,
                'message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Cancel a shipment in Jubelio.
     *
     * @param array $payload The payload for cancelling the shipment.
     *   $payload = [
     *       'shipment_id' => (int) The ID of the shipment to cancel.
     *       'awb_code' => (string) The Air Waybill code of the shipment.
     *       'cancel_reason' => (string) The reason for cancellation.
     *   ]
     * @return array|null
     */
    public function cancelShipment(array $payload): ?array
    {
        $client = $this->client();
        if (!$client) {
            Log::error('JubelioShipment: Failed to initialize API client for cancelShipment.');
            return null;
        }

        Log::info('JubelioShipment: Cancelling shipment with payload:', $payload);

        try {
            $response = $client->post('/shipments/cancel', $payload);

            if ($response->failed()) {
                Log::error('JubelioShipment: Failed to cancel shipment.', [
                    'request' => $payload,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('JubelioShipment: Successfully cancelled shipment.', [
                'request' => $payload,
                'response' => $responseData,
            ]);
            return $responseData;

        } catch (\Exception $e) {
            Log::error('JubelioShipment: Exception when cancelling shipment.', [
                'request' => $payload,
                'message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Create a new shipment in Jubelio.
     *
     * @param array $payload The payload for creating the shipment.
     *   $payload = [
     *       'ref_no' => '[auto]' The reference number for the shipment. Value must be: '[auto]'
     *       'courier_id' => (int) The ID of the courier.
     *       'courier_service_id' => (int) The ID of the courier service.
     *       'shipping_insurance' => (int) The value of the shipping insurance.
     *       'origin' => [
     *           'name' => (string) The name of the origin.
     *           'phone' => (string) The phone number of the origin.
     *           'address' => (string) The address of the origin.
     *           'note' => (string, optional) Additional notes for the origin.
     *           'q_address' => (string, optional) The query address for the origin.
     *           'area_id' => (string) The area ID of the origin.
     *           'zipcode' => (string) The zipcode of the origin.
     *           'coordinate' => (null|array) The coordinate of the origin.
     *           'email' => (string, optional) The email of the origin.
     *       ],
     *       'destination' => [
     *           'name' => (string) The name of the destination.
     *           'phone' => (string) The phone number of the destination.
     *           'address' => (string) The address of the destination.
     *           'note' => (string, optional) Additional notes for the destination.
     *           'q_address' => (string, optional) The query address for the destination.
     *           'area_id' => (string) The area ID of the destination.
     *           'zipcode' => (string) The zipcode of the destination.
     *           'coordinate' => (null|array) The coordinate of the destination.
     *           'email' => (string, optional) The email of the destination.
     *       ],
     *       'is_cod' => (bool) Whether the shipment is Cash on Delivery.
     *       'is_reserved_cod' => (bool) Whether the shipment is reserved for Cash on Delivery.
     *       'items' => [
     *           [
     *               'item_name' => (string) The name of the item.
     *               'value' => (int) The value of the item.
     *               'quantity' => (int) The quantity of the item.
     *               'length' => (int) The length of the item.
     *               'width' => (int) The width of the item.
     *               'height' => (int) The height of the item.
     *               'weight' => (int) The weight of the item.
     *               'category' => (string) The category of the item.
     *           ]
     *       ],
     *       'promotion_usage_id' => (null|int) The ID of the promotion usage.
     *   ]
     * @return array|null
     */
    public function createShipment(array $payload = []): ?array
    {
        $client = $this->client();
        if (!$client) {
            Log::error('JubelioShipment: Failed to initialize API client for createShipment.');
            return null;
        }

        Log::info('JubelioShipment: Creating shipment with payload:', $payload);

        try {
            $response = $client->post('/shipments/create', $payload);

            if ($response->failed()) {
                Log::error('JubelioShipment: Failed to create shipment.', [
                    'request' => $payload,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('JubelioShipment: Successfully created shipment.', [
                'request' => $payload,
                'response' => $responseData,
            ]);
            return $responseData;

        } catch (\Exception $e) {
            Log::error('JubelioShipment: Exception when creating shipment.', [
                'request' => $payload,
                'message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Create a new address in Jubelio.
     *
     * @param array $payload The payload for creating the address.
     * @return array|null
     */
    public function createAddress(array $payload = []): ?array
    {
        $client = $this->client();
        if (!$client) {
            Log::error('JubelioShipment: Failed to initialize API client for createAddress.');
            return null;
        }

        Log::info('JubelioShipment: Creating address with payload:', $payload);

        try {
            $response = $client->post('/setting/address', $payload);

            if ($response->failed()) {
                Log::error('JubelioShipment: Failed to create address.', [
                    'request' => $payload,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('JubelioShipment: Successfully created address.', [
                'request' => $payload,
                'response' => $responseData,
            ]);
            return $responseData;

        } catch (\Exception $e) {
            Log::error('JubelioShipment: Exception when creating address.', [
                'request' => $payload,
                'message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get rates from Jubelio.
     *
     * @param array $payload The payload for getting rates.
     *   $payload = [
     *       'origin' => [
     *           'area_id' => (string) The area ID of the origin.
     *           'zipcode' => (string) The zipcode of the origin.
     *           'coordinate' => (null|array) The coordinate of the origin.
     *       ],
     *       'destination' => [
     *           'area_id' => (string) The area ID of the destination.
     *           'zipcode' => (string) The zipcode of the destination.
     *       ],
     *       'service_category_id' => (string) The ID of the service category.
     *       'weight' => (int) The weight of the shipment.
     *       'total_value' => (int) The total value of the shipment.
     *       'items' => [
     *           [
     *               'item_name' => (string) The name of the item.
     *               'value' => (int) The value of the item.
     *               'quantity' => (int) The quantity of the item.
     *               'length' => (int) The length of the item.
     *               'width' => (int) The width of the item.
     *               'height' => (int) The height of the item.
     *               'weight' => (int) The weight of the item.
     *               'category' => [
     *                   'label' => (string) The label of the category.
     *                   'value' => (string) The value of the category.
     *               ]
     *           ]
     *       ]
     *   ]
     * @return array|null
     */
    public function getRates(array $payload = []): ?array
    {
        $client = $this->client();
        if (!$client) {
            Log::error('JubelioShipment: Failed to initialize API client for getRates.');
            return null;
        }

        Log::info('JubelioShipment: Getting rates with payload:', $payload);

        try {
            $response = $client->post('/rates', $payload);

            if ($response->failed()) {
                Log::error('JubelioShipment: Failed to get rates.', [
                    'request' => $payload,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return $response->json();
            }

            $responseData = $response->json();
            Log::info('JubelioShipment: Successfully retrieved rates.', [
                'request' => $payload,
                'response' => $responseData,
            ]);
            return $responseData;

        } catch (\Exception $e) {
            Log::error('JubelioShipment: Exception when getting rates.', [
                'request' => $payload,
                'message' => $e->getMessage(),
            ]);
            return null;
        }
    }


    public static function defaultVolume(): array
    {
        return [
            'length' => 30,
            'width' => 20,
            'height' => 10,
        ];
    }

    public static function defaultCategory(): array
    {
        return [
            'label' => 'Paket',
            'value' => 'Paket',
        ];
    }
}
