<?php

namespace App\Providers;

use App\Events\Jubelio\JubelioOrderStatus;
use App\Events\Xendit\XenditInvoiceExpired;
use App\Events\Xendit\XenditInvoicePaid;
use App\Listeners\Jubelio\HandleJubelioOrderStatus;
use App\Listeners\Xendit\HandleXenditInvoiceExpired;
use App\Listeners\Xendit\HandleXenditInvoicePaid;
use App\Models\CartItem;
use App\Models\Contact;
use App\Models\ContactSupport;
use App\Models\FrontendApp;
use App\Models\JneBranch;
use App\Models\JneDestination;
use App\Models\JneOrigin;
use App\Models\MembershipLevel;
use App\Models\Order;
use App\Models\ShippingAddress;
use App\Models\User;
use App\Policies\CartItemPolicy;
use App\Policies\ContactPolicy;
use App\Policies\FrontendAppPolicy;
use App\Policies\MembershipLevelPolicy;
use App\Policies\OrderPolicy;
use App\Policies\ShippingAddressPolicy;
use App\Policies\UserPolicy;
use App\Models\ProductDiscount;
use App\Policies\ContactSupportPolicy;
use App\Policies\JneBranchPolicy;
use App\Policies\JneDestinationPolicy;
use App\Policies\JneOriginPolicy;
use App\Policies\ProductDiscountPolicy;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Illuminate\Validation\Rules\Password;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Notifications\Messages\MailMessage;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Gate::policy(User::class, UserPolicy::class);
        Gate::policy(CartItem::class, CartItemPolicy::class);
        Gate::policy(Order::class, OrderPolicy::class);
        Gate::policy(ShippingAddress::class, ShippingAddressPolicy::class);
        Gate::policy(FrontendApp::class, FrontendAppPolicy::class);
        Gate::policy(ProductDiscount::class, ProductDiscountPolicy::class);
        Gate::policy(Contact::class, ContactPolicy::class);
        Gate::policy(ContactSupport::class, ContactSupportPolicy::class);
        Gate::policy(MembershipLevel::class, MembershipLevelPolicy::class);
        Gate::policy(JneBranch::class, JneBranchPolicy::class);
        Gate::policy(JneDestination::class, JneDestinationPolicy::class);
        Gate::policy(JneOrigin::class, JneOriginPolicy::class);

        Password::defaults(function () {
            $rule = Password::min(8)
                ->letters()
                ->mixedCase()
                ->numbers()
                ->uncompromised();

            return $rule;
        });

        Event::listen(
            XenditInvoicePaid::class,
            HandleXenditInvoicePaid::class,
        );

        Event::listen(
            XenditInvoiceExpired::class,
            HandleXenditInvoiceExpired::class,
        );

        Event::listen(
            JubelioOrderStatus::class,
            HandleJubelioOrderStatus::class,
        );

        VerifyEmail::toMailUsing(function (object $notifiable, string $url) {
            $frontendUrl = config('frontend.otoresell_url') . '/auth/verify-email?verify_url=' . urlencode($url);

            return (new MailMessage)
                ->subject('Welcome to ' . config('app.name') . '! Please Verify Your Email Address')
                ->greeting('Hello ' . $notifiable->name . ',')
                ->line('Thank you for registering with ' . config('app.name') . '. We are excited to have you on board.')
                ->line('Please click the button below to verify your email address and activate your account.')
                ->action('Verify Email Address', $frontendUrl)
                ->line('If you did not create an account, no further action is required.');
        });
    }
}
