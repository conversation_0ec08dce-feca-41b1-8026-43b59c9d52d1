<?php

namespace App\Http\Controllers\Api\Settings;

use App\Http\Controllers\Controller;
use App\Http\Resources\Settings\NotificationTypeResource;
use App\Models\NotificationType;
use App\Models\UserNotificationSetting;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class UserNotificationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // Eager-load the 'userNotificationSettings' relationship,
        // but only for the currently authenticated user.
        $notificationTypes = NotificationType::with(['userNotificationSettings' => function ($query) use ($user) {
            $query->where('user_id', $user->id);
        }])->get();

        return NotificationTypeResource::collection($notificationTypes);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = $request->user();

        // Validate the incoming data
        $validated = $request->validate([
            'settings' => ['required', 'array'],
            'settings.*.notification_type_id' => ['required', 'integer', Rule::exists('notification_types', 'id')],
            'settings.*.notification_channel_id' => ['required', 'integer', Rule::exists('notification_channels', 'id')],
        ]);

        foreach ($validated['settings'] as $setting) {
            UserNotificationSetting::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'notification_type_id' => $setting['notification_type_id'],
                ],
                [
                    'notification_channel_id' => $setting['notification_channel_id'],
                ]
            );
        }

        return response()->json(['message' => 'Settings updated successfully.'], 200);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
