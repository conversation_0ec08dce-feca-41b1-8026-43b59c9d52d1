<?php

namespace App\Http\Controllers\Api\Settings;

use App\Http\Controllers\Controller;
use App\Http\Resources\Settings\NotificationChannelResource;
use App\Http\Resources\Settings\NotificationTypeResource;
use App\Models\NotificationChannel;
use App\Models\NotificationType;
use App\Models\UserNotificationSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class UserNotificationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // Eager-load the 'userNotificationSettings' relationship,
        // but only for the currently authenticated user.

        $notificationTypes = NotificationType::with([
            'userNotificationSettings' => fn ($query) => $query->where('user_id', $user->id),
        ])->get();


        return NotificationTypeResource::collection($notificationTypes);
    }

    public function getNotificationChannels()
    {
        return NotificationChannelResource::collection(NotificationChannel::all());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = $request->user();

        // Validate the incoming data
        $validated = $request->validate([
            'settings' => ['required', 'array'],
            'settings.*' => ['required', 'array'],
            'settings.*.notification_type_id' => ['required', 'integer', 'exists:notification_types,id'],
            'settings.*.notification_channel_id' => ['required', 'integer', 'exists:notification_channels,id'],
        ]);

        Log::info('Validated data: ' . json_encode($validated));

        foreach ($validated['settings'] as $setting) {
            UserNotificationSetting::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'notification_type_id' => $setting['notification_type_id'],
                ],
                [
                    'notification_channel_id' => $setting['notification_channel_id'],
                ]
            );
        }

        return response()->json(['message' => 'Settings updated successfully.'], 200);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
