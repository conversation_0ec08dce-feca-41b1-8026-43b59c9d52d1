<?php

namespace App\Http\Controllers\Api\Settings;

use App\Http\Controllers\Controller;
use App\Http\Resources\Settings\NotificationChannelResource;
use App\Models\NotificationChannel;
use Illuminate\Http\Request;

class NotificationChannelController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return NotificationChannelResource::collection(NotificationChannel::all());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
