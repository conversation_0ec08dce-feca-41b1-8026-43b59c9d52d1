<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Order\IndexOrderRequest;
use App\Http\Requests\Order\StoreOrderRequest;
use App\Http\Requests\Order\UpdateOrderRequest;
use App\Http\Resources\Order\DetailOrderResource;
use App\Http\Resources\Order\IndexOrderResource;
use App\Models\Order;
use App\Models\Contact;
use App\Models\User;
use App\Notifications\OrderCreatedNotification;
use App\Notifications\OrderStatusUpdatedNotification;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use App\Models\Payment;
use App\Services\XenditService;
use App\Services\DiscountService;
use Illuminate\Support\Facades\Notification;

class OrderController extends Controller
{
    protected $xenditService;
    protected $discountService;

    public function __construct(XenditService $xenditService, DiscountService $discountService)
    {
        $this->xenditService = $xenditService;
        $this->discountService = $discountService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(IndexOrderRequest $request): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', Order::class);

        $user = Auth::user();
        // Log::info('User role: ' . $user->role);
        $validated = $request->validated();

        // Log::info('Validated data: ' . json_encode($validated));

        $query = Order::with('user', 'payments', 'latestPayment');

        // Apply user-based filtering
        if (!$user->isAdmin()) {
            // For resellers, filter by their own user_id
            $query->byUser($user->id);
        } elseif ($user->isAdmin()) {
            // For admins, optionally filter by specific user_id if provided
            if (isset($validated['user_id'])) {
                $query->byUser($validated['user_id']);
            }
            // If no user_id provided, admin sees all orders
        }

        // Apply other filters
        if (isset($validated['status'])) {
            $query->byStatus($validated['status']);
        }

        if (isset($validated['jubelio_contact_id'])) {
            $query->byContact($validated['jubelio_contact_id']);
        }

        if (isset($validated['jubelio_order_id'])) {
            $query->where('jubelio_order_id', $validated['jubelio_order_id']);
        }

        // Apply date range filter
        if (isset($validated['date_from'])) {
            $query->whereDate('created_at', '>=', $validated['date_from']);
        }

        if (isset($validated['date_to'])) {
            $query->whereDate('created_at', '<=', $validated['date_to']);
        }

        // Apply total range filter
        if (isset($validated['min_total'])) {
            $query->where('grand_total', '>=', $validated['min_total']);
        }

        if (isset($validated['max_total'])) {
            $query->where('grand_total', '<=', $validated['max_total']);
        }

        // Apply search
        if (isset($validated['search'])) {
            $query->search($validated['search'], $user->isAdmin() ? User::ROLE_ADMIN : User::ROLE_RESELLER);
        }

        // Apply sorting
        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        // Get results with pagination
        $orders = $query->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return IndexOrderResource::collection($orders);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreOrderRequest $request): JsonResponse
    {
        Gate::authorize('create', Order::class);

        $validated = $request->validated();
        $invoiceUrl = null;
        $user = $request->user()->load('membershipLevel');
        $membershipLevel = $user->membershipLevel;

        // --- New Contact Logic ---
        $contactForOrder = null;
        if ($user->role === User::ROLE_RESELLER) {
            $contactForOrder = Contact::where('purpose', Contact::PURPOSE_RESELLER_DEFAULT)->first();
        }
        // You can add an 'else' block here for other roles if needed

        if (!$contactForOrder) {
            return response()->json(['message' => 'No default reseller contact has been configured.'], 500);
        }

        // Add contact details to the validated data before creating the order
        $validated['contact_id'] = $contactForOrder->id;
        $validated['jubelio_contact_id'] = $contactForOrder->jubelio_contact_id;
        // --- End New Contact Logic ---

        $order = DB::transaction(function () use ($validated, $request, &$invoiceUrl, $membershipLevel) {
            // Calculate totals and get processed items from DiscountService
            $calculationResult = $this->discountService->calculateOrderTotals($request->items, $membershipLevel);

            // Add calculated totals to the validated data
            $validated['sub_total'] = $calculationResult['sub_total'];
            $validated['total_discount'] = $calculationResult['total_discount'];

            // Calculate grand_total
            $validated['grand_total'] = $validated['sub_total']
                - $validated['total_discount']
                + ($validated['shipping_cost'] ?? 0)
                + ($validated['insurance_cost'] ?? 0);

            // Create the order
            $order = Order::create($validated);

            // Create the order items with the data from the service
            if (!empty($calculationResult['items'])) {
                $order->items()->createMany($calculationResult['items']);
            }


            // Create a payment record
            $payment = $order->payments()->create([
                'external_id' => 'OTO-' . $order->id . '-' . time(),
                'payment_method' => $order->payment_method,
                'amount' => $order->grand_total,
                'currency' => 'IDR',
                'status' => 'pending',
            ]);

            // Eager load the user for Xendit service
            $order->load('user');

            // Create Xendit Invoice
            $xenditInvoice = $this->xenditService->createInvoiceXendit($order, $payment);

            if ($xenditInvoice) {
                $invoiceUrl = $xenditInvoice->getInvoiceUrl();
                // Update payment with Xendit info
                $payment->update([
                    'xendit_id' => $xenditInvoice->getId(),
                    'invoice_url' => $invoiceUrl,
                    'expires_at' => $xenditInvoice->getExpiryDate(),
                    'xendit_response' => $xenditInvoice,
                ]);
            }

            return $order;
        });

        $order->load(['items', 'shippingAddress', 'payments']); // Load other relationships

        // --- Send Notification ---
        // Prepare a collection of recipients, starting with all admins
        $recipients = User::where('role', User::ROLE_ADMIN)->get();

        // Add the user who created the order
        $recipients->push($request->user());

        // Send the notification to all unique recipients
        Notification::send($recipients->unique('id'), new OrderCreatedNotification($order, $request->user()));

        $resource = new DetailOrderResource($order);
        $data = $resource->toArray($request);
        $data['invoice_url'] = $invoiceUrl;

        Log::info('Order created: ' . json_encode($data));

        return response()->json([
            'message' => 'Order created successfully',
            'data' => $data
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Order $order): JsonResponse
    {
        Gate::authorize('view', $order);

        $order->load(['user', 'items', 'shippingAddress', 'payments', 'latestPayment']);

        return response()->json([
            'message' => 'Order retrieved successfully',
            'data' => new DetailOrderResource($order)
        ], 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateOrderRequest $request, Order $order): JsonResponse
    {
        Gate::authorize('update', $order);

        $validated = $request->validated();
        Log::info('Update Order Validated: ' . json_encode($validated));
        $oldStatus = $order->status;

        DB::transaction(function () use ($order, $validated, $request) {
            // Recalculate grand_total if related fields are updated
            if (isset($validated['sub_total']) || isset($validated['shipping_cost']) || isset($validated['total_discount'])) {
                $validated['grand_total'] = ($validated['sub_total'] ?? $order->sub_total)
                    + ($validated['shipping_cost'] ?? $order->shipping_cost)
                    - ($validated['total_discount'] ?? $order->total_discount);
            }

            $order->update($validated);

            // If items are included, sync them
            if ($request->has('items')) {
                $user = $request->user()->load('membershipLevel');
                $membershipLevel = $user->membershipLevel;

                // Calculate totals and get processed items from DiscountService
                $calculationResult = $this->discountService->calculateOrderTotals($request->items, $membershipLevel);

                // Delete existing items and create new ones
                $order->items()->delete();
                $order->items()->createMany($calculationResult['items']);

                // Update order totals from the service calculation
                $order->sub_total = $calculationResult['sub_total'];
                $order->total_discount = $calculationResult['total_discount'];
                $order->recalculateTotals(); // Recalculate grand_total with new values
                $order->save();
            }
        });

        $order->load(['user', 'items', 'shippingAddress', 'payments']); // Load all relationships

        // Check if status was updated and send notification
        if (isset($validated['status']) && $oldStatus !== $validated['status']) {
            // Get recipients for notification
            $recipients = collect();

            // Add all admins
            $admins = User::where('role', User::ROLE_ADMIN)->get();
            $recipients = $recipients->merge($admins);

            // Add the reseller who created the order (if not already included as admin)
            /** @var \App\Models\User $orderCreator */
            $orderCreator = $order->user;
            if (!$orderCreator->isAdmin()) {
                $recipients->push($orderCreator);
            }

            // Remove duplicates
            $recipients = $recipients->unique('id');

            // Send notifications
            Notification::send($recipients, new OrderStatusUpdatedNotification($order, $oldStatus, $validated['status'], $request->user()));
        }

        return response()->json([
            'message' => 'Order updated successfully',
            'data' => new DetailOrderResource($order->fresh())
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Order $order): JsonResponse
    {
        Gate::authorize('delete', $order);

        $order->delete();

        return response()->json([
            'message' => 'Order deleted successfully'
        ], 200);
    }

    /**
     * Remove multiple orders from storage.
     */
    public function destroyMultiple(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:orders,id'
        ]);

        $user = Auth::user();
        $query = Order::whereIn('id', $request->ids);

        // Apply user-based filtering for non-admin users
        if (!$user->isAdmin()) {
            $query->byUser($user->id);
        }

        $orders = $query->get();

        // Check authorization for each order
        foreach ($orders as $order) {
            Gate::authorize('delete', $order);
        }

        $deletedCount = $query->delete();

        return response()->json([
            'message' => "{$deletedCount} orders deleted successfully"
        ], 200);
    }

    /**
     * Get orders by contact ID.
     */
    public function getByContact(int $contactId): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', Order::class);

        $user = Auth::user();
        $query = Order::with('user')->byContact($contactId);

        // Apply user-based filtering for non-admin users
        if (!$user->isAdmin()) {
            $query->byUser($user->id);
        }

        $orders = $query->orderBy('created_at', 'desc')->get();

        return IndexOrderResource::collection($orders);
    }

    /**
     * Get order by order ID.
     */
    public function getByOrderId(int $orderId): JsonResponse
    {
        // Log::info('Order ID: ' . $orderId);
        $user = Auth::user();
        $query = Order::with('user')->where('jubelio_order_id', $orderId);

        // Apply user-based filtering for non-admin users
        if (!$user->isAdmin()) {
            $query->byUser($user->id);
        }

        $order = $query->first();
        // Log::info('Order: ' . json_encode($order));

        if (!$order) {
            return response()->json([
                'message' => 'Order not found'
            ], 404);
        }

        Gate::authorize('view', $order);

        return response()->json([
            'message' => 'Order retrieved successfully',
            'data' => new DetailOrderResource($order)
        ], 200);
    }

    /**
     * Update order status
     */
    public function updateStatus(Request $request, Order $order): JsonResponse
    {
        Gate::authorize('update', $order);

        $request->validate([
            'status' => 'required|string|in:' . implode(',', Order::getStatuses())
        ]);

        $oldStatus = $order->status;
        $newStatus = $request->status;

        // Only proceed if status actually changed
        if ($oldStatus !== $newStatus) {
            $order->update(['status' => $newStatus]);
            $order->load('user'); // Load the user relationship

            // Get recipients for notification
            $recipients = collect();

            // Add all admins
            $admins = User::where('role', User::ROLE_ADMIN)->get();
            $recipients = $recipients->merge($admins);

            // Add the reseller who created the order (if not already included as admin)
            /** @var \App\Models\User $orderCreator */
            $orderCreator = $order->user;
            if (!$orderCreator->isAdmin()) {
                $recipients->push($orderCreator);
            }

            // Remove duplicates and the user who made the update (optional)
            $recipients = $recipients->unique('id');
            // Uncomment the line below if you don't want to notify the user who made the update
            // $recipients = $recipients->reject(fn($user) => $user->id === Auth::id());

            // Send notifications
            Notification::send($recipients, new OrderStatusUpdatedNotification($order, $oldStatus, $newStatus, $request->user()));
        }

        return response()->json([
            'message' => 'Order status updated successfully',
            'data' => new DetailOrderResource($order->fresh())
        ], 200);
    }
}
