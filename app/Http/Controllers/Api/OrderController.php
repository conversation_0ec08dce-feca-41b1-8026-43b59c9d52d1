<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Order\IndexOrderRequest;
use App\Http\Requests\Order\StoreOrderRequest;
use App\Http\Requests\Order\UpdateOrderRequest;
use App\Http\Resources\Order\DetailOrderResource;
use App\Http\Resources\Order\IndexOrderResource;
use App\Models\Order;
use App\Models\Contact;
use App\Models\User;
use App\Notifications\OrderCreatedNotification;
use App\Notifications\OrderStatusUpdatedNotification;
use App\Models\SenderAddress;
use App\Models\ShippingAddress;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use App\Models\Payment;
use App\Services\XenditService;
use App\Services\DiscountService;
use App\Services\JneAirwaybillService;
use Illuminate\Support\Facades\Notification;
use App\Http\Requests\Order\GenerateJneAirwaybillRequest;
use App\Services\JubelioShipmentAirwaybillService;
use App\Services\CsvExportService;
use App\Http\Requests\Order\ExportOrderRequest;
use Symfony\Component\HttpFoundation\StreamedResponse;

class OrderController extends Controller
{
    protected $xenditService;
    protected $discountService;
    protected $jneAirwaybillService;
    protected $jubelioShipmentAirwaybillService;
    protected $csvExportService;

    public function __construct(
        XenditService $xenditService,
        DiscountService $discountService,
        JneAirwaybillService $jneAirwaybillService,
        JubelioShipmentAirwaybillService $jubelioShipmentAirwaybillService,
        CsvExportService $csvExportService
    ) {
        $this->xenditService = $xenditService;
        $this->discountService = $discountService;
        $this->jneAirwaybillService = $jneAirwaybillService;
        $this->jubelioShipmentAirwaybillService = $jubelioShipmentAirwaybillService;
        $this->csvExportService = $csvExportService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(IndexOrderRequest $request): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', Order::class);

        $user = Auth::user();
        // Log::info('User role: ' . $user->role);
        $validated = $request->validated();

        // Log::info('Validated data: ' . json_encode($validated));

        $query = Order::with('user', 'payments', 'latestPayment');

        // Apply user-based filtering
        if (!$user->isAdmin()) {
            // For resellers, filter by their own user_id
            $query->byUser($user->id);
        } elseif ($user->isAdmin()) {
            // For admins, optionally filter by specific user_id if provided
            if (isset($validated['user_id'])) {
                $query->byUser($validated['user_id']);
            }
            // If no user_id provided, admin sees all orders
        }

        // Apply other filters
        if (isset($validated['status'])) {
            $query->byStatus($validated['status']);
        }

        if (isset($validated['jubelio_contact_id'])) {
            $query->byContact($validated['jubelio_contact_id']);
        }

        if (isset($validated['jubelio_order_id'])) {
            $query->where('jubelio_order_id', $validated['jubelio_order_id']);
        }

        // Apply date range filter
        if (isset($validated['date_from'])) {
            $query->whereDate('created_at', '>=', $validated['date_from']);
        }

        if (isset($validated['date_to'])) {
            $query->whereDate('created_at', '<=', $validated['date_to']);
        }

        // Apply total range filter
        if (isset($validated['min_total'])) {
            $query->where('grand_total', '>=', $validated['min_total']);
        }

        if (isset($validated['max_total'])) {
            $query->where('grand_total', '<=', $validated['max_total']);
        }

        // Apply search
        if (isset($validated['search'])) {
            $query->search($validated['search'], $user->isAdmin() ? User::ROLE_ADMIN : User::ROLE_RESELLER);
        }

        // Apply sorting
        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        // Get results with pagination
        $orders = $query->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return IndexOrderResource::collection($orders);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreOrderRequest $request): JsonResponse
    {
        Gate::authorize('create', Order::class);

        $validated = $request->validated();
        $invoiceUrl = null;
        $user = $request->user();
        $user->load('membershipLevel');

        $shippingAddress = ShippingAddress::find($validated['shipping_address_id']);
        $senderAddress = isset($validated['sender_address_id']) ? SenderAddress::find($validated['sender_address_id']) : null;

        // Prepare snapshot data
        $snapshotData = [
            'user_id' => $user->id,
            'user_name' => $user->name, // Use the correct user's name
            'user_phone' => $user->phone, // Use the correct user's phone
            'shipping_full_name' => $shippingAddress->shipping_full_name,
            'shipping_phone' => $shippingAddress->shipping_phone,
            'shipping_address' => $shippingAddress->shipping_address,
            'shipping_province_id' => $shippingAddress->shipping_province_id,
            'shipping_province' => $shippingAddress->shipping_province,
            'shipping_city_id' => $shippingAddress->shipping_city_id,
            'shipping_city' => $shippingAddress->shipping_city,
            'shipping_district_id' => $shippingAddress->shipping_district_id,
            'shipping_district' => $shippingAddress->shipping_district,
            'shipping_subdistrict_id' => $shippingAddress->shipping_subdistrict_id,
            'shipping_subdistrict' => $shippingAddress->shipping_subdistrict,
            'shipping_post_code' => $shippingAddress->shipping_post_code,
        ];

        if ($senderAddress) {
            $snapshotData = array_merge($snapshotData, [
                'sender_full_name' => $senderAddress->sender_full_name,
                'sender_phone' => $senderAddress->sender_phone,
                'sender_address' => $senderAddress->sender_address,
                'sender_province_id' => $senderAddress->sender_province_id,
                'sender_province' => $senderAddress->sender_province,
                'sender_city_id' => $senderAddress->sender_city_id,
                'sender_city' => $senderAddress->sender_city,
                'sender_district_id' => $senderAddress->sender_district_id,
                'sender_district' => $senderAddress->sender_district,
                'sender_subdistrict_id' => $senderAddress->sender_subdistrict_id,
                'sender_subdistrict' => $senderAddress->sender_subdistrict,
                'sender_post_code' => $senderAddress->sender_post_code,
            ]);
        }
        $membershipLevel = $user->membershipLevel;

        // --- New Contact Logic ---
        $contactForOrder = null;
        if ($user->role === User::ROLE_RESELLER) {
            $contactForOrder = Contact::where('purpose', Contact::PURPOSE_RESELLER_DEFAULT)->first();
        }
        // You can add an 'else' block here for other roles if needed

        if (!$contactForOrder) {
            return response()->json(['message' => 'No default reseller contact has been configured.'], 500);
        }

        // Add contact details to the validated data before creating the order
        $validated['contact_id'] = $contactForOrder->id;
        $validated['jubelio_contact_id'] = $contactForOrder->jubelio_contact_id;
        // --- End New Contact Logic ---

        $order = DB::transaction(function () use ($validated, $request, &$invoiceUrl, $membershipLevel, $snapshotData) {
            // Calculate totals and get processed items from DiscountService
            $calculationResult = $this->discountService->calculateOrderTotals($request->items, $membershipLevel);

            // Add calculated totals to the validated data
            $validated['sub_total'] = $calculationResult['sub_total'];
            $validated['total_discount'] = $calculationResult['total_discount'];

            // Calculate grand_total
            $validated['grand_total'] = $validated['sub_total']
                - $validated['total_discount']
                + ($validated['shipping_cost'] ?? 0)
                + ($validated['insurance_cost'] ?? 0);

            // Create the order
            $order = Order::create(array_merge($validated, $snapshotData));

            // Create the order items with the data from the service
            if (!empty($calculationResult['items'])) {
                $order->items()->createMany($calculationResult['items']);
            }


            // Create a payment record
            $payment = $order->payments()->create([
                'external_id' => 'OTO-' . $order->id . '-' . time(),
                'payment_method' => $order->payment_method,
                'amount' => $order->grand_total,
                'currency' => 'IDR',
                'status' => 'pending',
            ]);

            // Eager load the user for Xendit service
            $order->load('user');

            // Create Xendit Invoice
            $xenditInvoice = $this->xenditService->createInvoiceXendit($order, $payment);

            if ($xenditInvoice) {
                $invoiceUrl = $xenditInvoice->getInvoiceUrl();
                // Update payment with Xendit info
                $payment->update([
                    'xendit_id' => $xenditInvoice->getId(),
                    'invoice_url' => $invoiceUrl,
                    'expires_at' => $xenditInvoice->getExpiryDate(),
                    'xendit_response' => $xenditInvoice,
                ]);
            }

            return $order;
        });

        $order->load(['items', 'shippingAddress', 'payments']); // Load other relationships

        // --- Send Notification ---
        // Prepare a collection of recipients, starting with all admins
        $recipients = User::where('role', User::ROLE_ADMIN)->get();

        // Add the user who created the order
        $recipients->push($request->user());

        // Send the notification to all unique recipients
        Notification::send($recipients->unique('id'), new OrderCreatedNotification($order, $request->user()));

        $resource = new DetailOrderResource($order);
        $data = $resource->toArray($request);
        $data['invoice_url'] = $invoiceUrl;

        Log::info('Order created: ' . json_encode($data));

        return response()->json([
            'message' => 'Order created successfully',
            'data' => $data
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Order $order): JsonResponse
    {
        Gate::authorize('view', $order);

        $order->load(['user', 'items', 'shippingAddress', 'payments', 'latestPayment']);

        return response()->json([
            'message' => 'Order retrieved successfully',
            'data' => new DetailOrderResource($order)
        ], 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateOrderRequest $request, Order $order): JsonResponse
    {
        Gate::authorize('update', $order);

        $validated = $request->validated();
        Log::info('Update Order Validated: ' . json_encode($validated));
        $oldStatus = $order->status;

        // Prepare snapshot data if address or user IDs are present
        $snapshotData = [];

        // If the user is being changed, update the user snapshot fields
        if (isset($validated['user_id']) && $validated['user_id'] !== $order->user_id) {
            $newUser = User::find($validated['user_id']);
            $snapshotData['user_name'] = $newUser->name;
            $snapshotData['user_phone'] = $newUser->phone;
        }

        if (isset($validated['shipping_address_id'])) {
            $shippingAddress = ShippingAddress::find($validated['shipping_address_id']);
            $snapshotData = array_merge($snapshotData, [
                'shipping_full_name' => $shippingAddress->shipping_full_name,
                'shipping_phone' => $shippingAddress->shipping_phone,
                'shipping_address' => $shippingAddress->shipping_address,
                'shipping_province_id' => $shippingAddress->shipping_province_id,
                'shipping_province' => $shippingAddress->shipping_province,
                'shipping_city_id' => $shippingAddress->shipping_city_id,
                'shipping_city' => $shippingAddress->shipping_city,
                'shipping_district_id' => $shippingAddress->shipping_district_id,
                'shipping_district' => $shippingAddress->shipping_district,
                'shipping_subdistrict_id' => $shippingAddress->shipping_subdistrict_id,
                'shipping_subdistrict' => $shippingAddress->shipping_subdistrict,
                'shipping_post_code' => $shippingAddress->shipping_post_code,
            ]);
        }

        if (isset($validated['sender_address_id'])) {
            $senderAddress = SenderAddress::find($validated['sender_address_id']);
            if ($senderAddress) {
                $snapshotData = array_merge($snapshotData, [
                    'sender_full_name' => $senderAddress->sender_full_name,
                    'sender_phone' => $senderAddress->sender_phone,
                    'sender_address' => $senderAddress->sender_address,
                    'sender_province_id' => $senderAddress->sender_province_id,
                    'sender_province' => $senderAddress->sender_province,
                    'sender_city_id' => $senderAddress->sender_city_id,
                    'sender_city' => $senderAddress->sender_city,
                    'sender_district_id' => $senderAddress->sender_district_id,
                    'sender_district' => $senderAddress->sender_district,
                    'sender_subdistrict_id' => $senderAddress->sender_subdistrict_id,
                    'sender_subdistrict' => $senderAddress->sender_subdistrict,
                    'sender_post_code' => $senderAddress->sender_post_code,
                ]);
            }
        }


        DB::transaction(function () use ($order, $validated, $request, $snapshotData) {
            // Recalculate grand_total if related fields are updated
            if (isset($validated['sub_total']) || isset($validated['shipping_cost']) || isset($validated['total_discount'])) {
                $validated['grand_total'] = ($validated['sub_total'] ?? $order->sub_total)
                    + ($validated['shipping_cost'] ?? $order->shipping_cost)
                    - ($validated['total_discount'] ?? $order->total_discount);
            }

            $order->update(array_merge($validated, $snapshotData));

            // If items are included, sync them
            if ($request->has('items')) {
                $user = $request->user()->load('membershipLevel');
                $membershipLevel = $user->membershipLevel;

                // Calculate totals and get processed items from DiscountService
                $calculationResult = $this->discountService->calculateOrderTotals($request->items, $membershipLevel);

                // Delete existing items and create new ones
                $order->items()->delete();
                $order->items()->createMany($calculationResult['items']);

                // Update order totals from the service calculation
                $order->sub_total = $calculationResult['sub_total'];
                $order->total_discount = $calculationResult['total_discount'];
                $order->recalculateTotals(); // Recalculate grand_total with new values
                $order->save();
            }
        });

        $order->load(['user', 'items', 'shippingAddress', 'payments']); // Load all relationships

        // Check if status was updated and send notification
        if (isset($validated['status']) && $oldStatus !== $validated['status']) {
            // Get recipients for notification
            $recipients = collect();

            // Add all admins
            $admins = User::where('role', User::ROLE_ADMIN)->get();
            $recipients = $recipients->merge($admins);

            // Add the reseller who created the order (if not already included as admin)
            /** @var \App\Models\User $orderCreator */
            $orderCreator = $order->user;
            if (!$orderCreator->isAdmin()) {
                $recipients->push($orderCreator);
            }

            // Remove duplicates
            $recipients = $recipients->unique('id');

            // Send notifications
            Notification::send($recipients, new OrderStatusUpdatedNotification($order, $oldStatus, $validated['status'], $request->user()));
        }

        return response()->json([
            'message' => 'Order updated successfully',
            'data' => new DetailOrderResource($order->fresh())
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Order $order): JsonResponse
    {
        Gate::authorize('delete', $order);

        $order->delete();

        return response()->json([
            'message' => 'Order deleted successfully'
        ], 200);
    }

    /**
     * Remove multiple orders from storage.
     */
    public function destroyMultiple(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:orders,id'
        ]);

        $user = Auth::user();
        $query = Order::whereIn('id', $request->ids);

        // Apply user-based filtering for non-admin users
        if (!$user->isAdmin()) {
            $query->byUser($user->id);
        }

        $orders = $query->get();

        // Check authorization for each order
        foreach ($orders as $order) {
            Gate::authorize('delete', $order);
        }

        $deletedCount = $query->delete();

        return response()->json([
            'message' => "{$deletedCount} orders deleted successfully"
        ], 200);
    }

    /**
     * Get orders by contact ID.
     */
    public function getByContact(int $contactId): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', Order::class);

        $user = Auth::user();
        $query = Order::with('user')->byContact($contactId);

        // Apply user-based filtering for non-admin users
        if (!$user->isAdmin()) {
            $query->byUser($user->id);
        }

        $orders = $query->orderBy('created_at', 'desc')->get();

        return IndexOrderResource::collection($orders);
    }

    /**
     * Get order by order ID.
     */
    public function getByOrderId(int $orderId): JsonResponse
    {
        // Log::info('Order ID: ' . $orderId);
        $user = Auth::user();
        $query = Order::with('user')->where('jubelio_order_id', $orderId);

        // Apply user-based filtering for non-admin users
        if (!$user->isAdmin()) {
            $query->byUser($user->id);
        }

        $order = $query->first();
        // Log::info('Order: ' . json_encode($order));

        if (!$order) {
            return response()->json([
                'message' => 'Order not found'
            ], 404);
        }

        Gate::authorize('view', $order);

        return response()->json([
            'message' => 'Order retrieved successfully',
            'data' => new DetailOrderResource($order)
        ], 200);
    }

    /**
     * Update order status
     */
    public function updateStatus(Request $request, Order $order): JsonResponse
    {
        Gate::authorize('update', $order);

        $request->validate([
            'status' => 'required|string|in:' . implode(',', Order::getStatuses())
        ]);

        $oldStatus = $order->status;
        $newStatus = $request->status;

        // Only proceed if status actually changed
        if ($oldStatus !== $newStatus) {
            $order->update(['status' => $newStatus]);
            $order->load('user'); // Load the user relationship

            // Get recipients for notification
            $recipients = collect();

            // Add all admins
            $admins = User::where('role', User::ROLE_ADMIN)->get();
            $recipients = $recipients->merge($admins);

            // Add the reseller who created the order (if not already included as admin)
            /** @var \App\Models\User $orderCreator */
            $orderCreator = $order->user;
            if (!$orderCreator->isAdmin()) {
                $recipients->push($orderCreator);
            }

            // Remove duplicates and the user who made the update (optional)
            $recipients = $recipients->unique('id');
            // Uncomment the line below if you don't want to notify the user who made the update
            // $recipients = $recipients->reject(fn($user) => $user->id === Auth::id());

            // Send notifications
            Notification::send($recipients, new OrderStatusUpdatedNotification($order, $oldStatus, $newStatus, $request->user()));
        }

        return response()->json([
            'message' => 'Order status updated successfully',
            'data' => new DetailOrderResource($order->fresh())
        ], 200);
    }

    public function generateJneAirwaybill(GenerateJneAirwaybillRequest $request): JsonResponse
    {
        $validated = $request->validated();
        Log::info('Validated data: ' . json_encode($validated));
        $order = Order::find($validated['order_id']);

        if ($order->shipping_type !== Order::SHIPPING_TYPE_JNE_EXPEDITION) {
            return response()->json([
                'message' => 'This order is not a JNE expedition type.',
            ], 422);
        }

        $result = $this->jneAirwaybillService->handleJneExpedition($order);

        if ($result['success']) {
            return response()->json([
                'message' => $result['message'],
                'data' => new DetailOrderResource($order->fresh()),
            ], 200);
        }

        return response()->json([
            'message' => $result['message'],
        ], 500);
    }

    public function generateJubelioShipmentAirwaybill(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'order_id' => 'required|integer|exists:orders,id',
        ]);

        $order = Order::find($validated['order_id']);

        if ($order->shipping_type !== Order::SHIPPING_TYPE_JUBELIO_SHIPMENT) {
            return response()->json([
                'message' => 'This order is not a Jubelio shipment type.',
            ], 422);
        }

        $result = $this->jubelioShipmentAirwaybillService->handle($order);

        if ($result['success']) {
            return response()->json([
                'message' => $result['message'],
                'data' => new DetailOrderResource($order->fresh()),
            ], 200);
        }

        return response()->json([
            'message' => $result['message'],
        ], 500);
    }
    public function exportCsv(ExportOrderRequest $request): StreamedResponse
    {
        $validated = $request->validated();

        $query = Order::query();

        // Apply date range filter
        if (isset($validated['date_from'])) {
            $query->whereDate('created_at', '>=', $validated['date_from']);
        }

        if (isset($validated['date_to'])) {
            $query->whereDate('created_at', '<=', $validated['date_to']);
        }

        // Apply status filter
        if (isset($validated['status'])) {
            $query->byStatus($validated['status']);
        }

        $headings = [
            'ID',
            'Contact ID',
            'Jubelio Contact ID',
            'Jubelio Order ID',
            'User ID',
            'Shipping Address ID',
            'Sender Address ID',
            'User Name',
            'User Phone',
            'Shipping Full Name',
            'Shipping Phone',
            'Shipping Address',
            'Shipping Province ID',
            'Shipping Province',
            'Shipping City ID',
            'Shipping City',
            'Shipping District ID',
            'Shipping District',
            'Shipping Subdistrict ID',
            'Shipping Subdistrict',
            'Shipping Post Code',
            'Status',
            'Shipping Type',
            'Tracking Number',
            'Sub Total',
            'Total Discount',
            'Shipping Cost',
            'Insurance Cost',
            'Grand Total',
            'Payment Method',
            'Expedition',
            'Etc',
            'Sender Full Name',
            'Sender Phone',
            'Sender Address',
            'Sender Province ID',
            'Sender Province',
            'Sender City ID',
            'Sender City',
            'Sender District ID',
            'Sender District',
            'Sender Subdistrict ID',
            'Sender Subdistrict',
            'Sender Post Code',
            'Created At',
            'Updated At',
        ];

        $filename = 'orders-export-' . now()->format('Y-m-d-His') . '.csv';

        return $this->csvExportService->streamCollection(
            $query,
            $filename,
            $headings,
            function (Order $order) {
                return [
                    'ID' => $order->id,
                    'Contact ID' => $order->contact_id,
                    'Jubelio Contact ID' => $order->jubelio_contact_id,
                    'Jubelio Order ID' => $order->jubelio_order_id,
                    'User ID' => $order->user_id,
                    'Shipping Address ID' => $order->shipping_address_id,
                    'Sender Address ID' => $order->sender_address_id,
                    'User Name' => $order->user_name,
                    'User Phone' => $order->user_phone,
                    'Shipping Full Name' => $order->shipping_full_name,
                    'Shipping Phone' => $order->shipping_phone,
                    'Shipping Address' => $order->shipping_address,
                    'Shipping Province ID' => $order->shipping_province_id,
                    'Shipping Province' => $order->shipping_province,
                    'Shipping City ID' => $order->shipping_city_id,
                    'Shipping City' => $order->shipping_city,
                    'Shipping District ID' => $order->shipping_district_id,
                    'Shipping District' => $order->shipping_district,
                    'Shipping Subdistrict ID' => $order->shipping_subdistrict_id,
                    'Shipping Subdistrict' => $order->shipping_subdistrict,
                    'Shipping Post Code' => $order->shipping_post_code,
                    'Status' => $order->status,
                    'Shipping Type' => $order->shipping_type,
                    'Tracking Number' => $order->tracking_number,
                    'Sub Total' => $order->sub_total,
                    'Total Discount' => $order->total_discount,
                    'Shipping Cost' => $order->shipping_cost,
                    'Insurance Cost' => $order->insurance_cost,
                    'Grand Total' => $order->grand_total,
                    'Payment Method' => $order->payment_method,
                    'Expedition' => json_encode($order->expedition),
                    'Etc' => json_encode($order->etc),
                    'Sender Full Name' => $order->sender_full_name,
                    'Sender Phone' => $order->sender_phone,
                    'Sender Address' => $order->sender_address,
                    'Sender Province ID' => $order->sender_province_id,
                    'Sender Province' => $order->sender_province,
                    'Sender City ID' => $order->sender_city_id,
                    'Sender City' => $order->sender_city,
                    'Sender District ID' => $order->sender_district_id,
                    'Sender District' => $order->sender_district,
                    'Sender Subdistrict ID' => $order->sender_subdistrict_id,
                    'Sender Subdistrict' => $order->sender_subdistrict,
                    'Sender Post Code' => $order->sender_post_code,
                    'Created At' => $order->created_at->toDateTimeString(),
                    'Updated At' => $order->updated_at->toDateTimeString(),
                ];
            }
        );
    }
}
