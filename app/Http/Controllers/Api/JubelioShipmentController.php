<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\JubelioShipment\GetRegionsRequest;
use App\Http\Requests\JubelioShipment\GetRatesRequest;
use App\Models\Order;
use App\Services\JubelioShipment;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;

class JubelioShipmentController extends Controller
{
    protected $jubelioShipment;

    public function __construct(JubelioShipment $jubelioShipment)
    {
        $this->jubelioShipment = $jubelioShipment;
    }

    /**
     * Get a list of regions from Jubelio.
     */
    public function getRegions(GetRegionsRequest $request): JsonResponse
    {
        Gate::authorize('getRegions', JubelioShipment::class);

        $validated = $request->validated();

        $regions = $this->jubelioShipment->getRegions($validated['name']);

        if (is_null($regions)) {
            return response()->json(['message' => 'Failed to retrieve regions.'], 502);
        }

        return response()->json($regions);
    }

    /**
     * Get a list of service categories from Jubelio.
     */
    public function getServiceCategories(): JsonResponse
    {
        Gate::authorize('getServiceCategories', JubelioShipment::class);

        $serviceCategories = $this->jubelioShipment->getServiceCategories();

        if (is_null($serviceCategories)) {
            return response()->json(['message' => 'Failed to retrieve service categories.'], 502);
        }

        return response()->json($serviceCategories);
    }

    /**
     * Get rates from Jubelio.
     */
    public function getRates(GetRatesRequest $request): JsonResponse
    {
        Gate::authorize('getRates', JubelioShipment::class);

        $validated = $request->validated();

        $rates = $this->jubelioShipment->getRates($validated);
        Log::info('Rates:', $rates);

        if (is_null($rates)) {
            return response()->json(['message' => 'Failed to retrieve rates.'], 502);
        }

        if (isset($rates['code'])) {
            return response()->json($rates, 422);
        }

        return response()->json($rates);
    }

    /**
     * Get the log of a specific shipment from Jubelio.
     */
    public function getShipmentLog(Order $order): JsonResponse
    {
        Gate::authorize('getShipmentLog', JubelioShipment::class);

        if ($order->shipping_type !== Order::SHIPPING_TYPE_JUBELIO_SHIPMENT) {
            return response()->json(['message' => 'This order was not shipped with Jubelio Shipment.'], 422);
        }

        $etc = $order->etc;
        $shipmentId = $etc['jubelio_shipment']['shipment_id'] ?? null;
        Log::info('Shipment ID: ' . $shipmentId);

        if (!$shipmentId) {
            return response()->json(['message' => 'Shipment ID not found for this order.'], 404);
        }

        $shipmentLog = $this->jubelioShipment->getShipmentLog($shipmentId);

        if (is_null($shipmentLog)) {
            return response()->json(['message' => 'Failed to retrieve shipment log.'], 502);
        }

        return response()->json($shipmentLog);
    }
}
