<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\StreamedResponse;

class NotificationController extends Controller
{
    /**
     * Get all notifications for the authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $page = $request->get('page', 1);

        $notifications = $request->user()
            ->notifications()
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'message' => 'Notifications retrieved successfully',
            'data' => $notifications
        ], 200);
    }

    /**
     * Get unread notifications count
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function unreadCount(Request $request): JsonResponse
    {
        $count = $request->user()->unreadNotifications()->count();

        return response()->json([
            'message' => 'Unread notifications count retrieved successfully',
            'data' => [
                'unread_count' => $count
            ]
        ], 200);
    }

    /**
     * Mark notification as read
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function markAsRead(Request $request, string $id): JsonResponse
    {
        $notification = $request->user()
            ->notifications()
            ->where('id', $id)
            ->first();

        if (!$notification) {
            return response()->json([
                'message' => 'Notification not found'
            ], 404);
        }

        $notification->markAsRead();

        return response()->json([
            'message' => 'Notification marked as read successfully'
        ], 200);
    }

    /**
     * Mark all notifications as read
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function markAllAsRead(Request $request): JsonResponse
    {
        $request->user()->unreadNotifications()->update(['read_at' => now()]);

        return response()->json([
            'message' => 'All notifications marked as read successfully'
        ], 200);
    }

    /**
     * Delete a notification
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function destroy(Request $request, string $id): JsonResponse
    {
        $notification = $request->user()
            ->notifications()
            ->where('id', $id)
            ->first();

        if (!$notification) {
            return response()->json([
                'message' => 'Notification not found'
            ], 404);
        }

        $notification->delete();

        return response()->json([
            'message' => 'Notification deleted successfully'
        ], 200);
    }

    /**
     * Clear all notifications
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function clear(Request $request): JsonResponse
    {
        $request->user()->notifications()->delete();

        return response()->json([
            'message' => 'All notifications cleared successfully'
        ], 200);
    }

    /**
     * Stream notifications via Server-Sent Events (not used yet)
     *
     * @param Request $request
     * @return StreamedResponse
     */
    public function stream(Request $request): StreamedResponse
    {
        $response = new StreamedResponse(function () use ($request) {
            $user = $request->user();
            $lastNotificationId = $request->header('Last-Event-ID');

            // Set execution time limit for long-running connection
            set_time_limit(0);

            while (true) {
                // Check for new notifications
                $query = $user->notifications()->orderBy('created_at', 'desc');

                if ($lastNotificationId) {
                    $query->where('created_at', '>',
                        $user->notifications()->find($lastNotificationId)->created_at ?? now()->subMinutes(5)
                    );
                }

                $newNotifications = $query->limit(10)->get();

                if ($newNotifications->isNotEmpty()) {
                    foreach ($newNotifications as $notification) {
                        echo "id: {$notification->id}\n";
                        echo "event: notification\n";
                        echo "data: " . json_encode([
                            'type' => 'new_notification',
                            'notification' => $notification,
                            'unread_count' => $user->unreadNotifications()->count()
                        ]) . "\n\n";
                    }

                    ob_flush();
                    flush();
                }

                // Send heartbeat every 30 seconds
                echo "event: heartbeat\n";
                echo "data: " . json_encode(['timestamp' => now()->toISOString()]) . "\n\n";
                ob_flush();
                flush();

                // Sleep for 5 seconds before next check
                sleep(5);

                // Check if connection is still alive
                if (connection_aborted()) {
                    break;
                }
            }
        });

        $response->headers->set('Content-Type', 'text/event-stream');
        $response->headers->set('Cache-Control', 'no-cache');
        $response->headers->set('Connection', 'keep-alive');
        $response->headers->set('Access-Control-Allow-Origin', '*');
        $response->headers->set('Access-Control-Allow-Credentials', 'true');
        $response->headers->set('Access-Control-Allow-Headers', 'Last-Event-ID');

        return $response;
    }
}
