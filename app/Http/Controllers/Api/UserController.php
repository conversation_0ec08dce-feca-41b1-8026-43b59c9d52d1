<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\IndexUserRequest;
use App\Http\Requests\User\StoreUserRequest;
use App\Http\Requests\User\UpdateUserRequest;
use App\Http\Requests\User\UpdateProfileRequest;
use App\Http\Requests\User\ChangePasswordRequest;
use App\Http\Resources\User\UserResource;
use App\Models\MembershipLevel;
use App\Models\User;
use App\Models\FrontendApp;
use App\Notifications\UserCreatedNotification;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Carbon\Carbon;

class UserController extends Controller
{
    /**
     * Display a listing of users
     *
     * @param IndexUserRequest $request
     * @return AnonymousResourceCollection
     */
    public function index(IndexUserRequest $request): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', User::class);

        $validated = $request->validated();
        // Log::info('Validated data: ' . json_encode($validated));

        $query = User::with('membershipLevel');

        // Search functionality
        if (!empty($validated['search'])) {
            $query->where(function ($q) use ($validated) {
                $q->where('name', 'like', "%{$validated['search']}%")
                  ->orWhere('email', 'like', "%{$validated['search']}%")
                  ->orWhereHas('membershipLevel', function ($query) use ($validated) {
                      $query->where('name', 'like', "%{$validated['search']}%");
                  });
            });
        }

        // Filter by role
        if (!empty($validated['role'])) {
            $query->where('role', $validated['role']);
        }

        // Filter by is_active
        if (isset($validated['is_active'])) {
            $query->where('is_active', $validated['is_active']);
        }

        // Apply sorting
        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        $users = $query->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return UserResource::collection($users);
    }

    /**
     * Store a newly created user
     *
     * @param StoreUserRequest $request
     * @return JsonResponse
     */
    public function store(StoreUserRequest $request): JsonResponse
    {
        Gate::authorize('create', User::class);

        $validated = $request->validated();

        $role = $validated['role'];

        // Pre-check: Ensure at least one FrontendApp is configured for the selected role.
        $appExistsForRole = FrontendApp::where('auto_assign_to_role', $role)->exists();

        if (!$appExistsForRole) {
            return response()->json([
                'message' => 'Validation Error',
                'errors' => [
                    'role' => ["No frontend apps are configured for the '{$role}' role. Please configure at least one app for this role before creating a user."]
                ]
            ], 422);
        }

        // If the new user is an admin, automatically verify their email.
        if (isset($validated['role']) && $validated['role'] === User::ROLE_ADMIN) {
            $validated['email_verified_at'] = Carbon::now();
        }

        // Assign default membership level if not provided
        if (empty($validated['membership_level_id'])) {
            $defaultLevel = MembershipLevel::where('is_default', true)->first();
            if ($defaultLevel) {
                $validated['membership_level_id'] = $defaultLevel->id;
            }
            // If no default level is found, the request will fail validation if the field is required,
            // or proceed with a null value if it's nullable. This logic assumes a default level should exist.
        }

        // Password is automatically hashed by the model cast
        $user = User::create($validated);

        // --- App Assignment Logic ---
        // Automatically assign all apps that are configured for the new user's role.
        $appsToAssign = FrontendApp::where('auto_assign_to_role', $role)->pluck('id');
        $user->frontendApps()->sync($appsToAssign);

        // --- Notification Logic ---
        $creatingAdmin = $request->user();

        // If an admin creates another admin, notify the new admin and all existing admins.
        if ($user->isAdmin()) {
            // Get all admins, including the newly created one.
            $recipients = User::where('role', User::ROLE_ADMIN)->get();
            Notification::send($recipients, new UserCreatedNotification($user, $creatingAdmin));
        } else {
            // If an admin creates a reseller, only notify other admins.
            // The reseller will get a WelcomeNotification after email verification.
            $adminUsers = User::where('role', User::ROLE_ADMIN)
                              ->where('id', '!=', $creatingAdmin->id) // Exclude the creator
                              ->get();
            Notification::send($adminUsers, new UserCreatedNotification($user, $creatingAdmin));
        }

        return response()->json([
            'message' => 'User created successfully',
            'data' => new UserResource($user)
        ], 201);
    }

    /**
     * Display the specified user
     *
     * @param User $user
     * @return JsonResponse
     */
    public function show(User $user): JsonResponse
    {
        Gate::authorize('view', $user);

        return response()->json([
            'message' => 'User retrieved successfully',
            'data' => new UserResource($user->load(['frontendApps', 'membershipLevel']))
        ], 200);
    }

    /**
     * Update the specified user (Admin only)
     *
     * @param UpdateUserRequest $request
     * @param User $user
     * @return JsonResponse
     */
    public function update(UpdateUserRequest $request, User $user): JsonResponse
    {
        Gate::authorize('update', $user);

        $validated = $request->validated();
        // Log::info($validated);
        // Log::info($user);

        // Password is automatically hashed by the model cast if provided
        $user->update($validated);

        // The frontend_app_ids are now managed by the FrontendAppController when the
        // `auto_assign_to_role` is changed, so this is no longer needed.

        return response()->json([
            'message' => 'User updated successfully',
            'data' => new UserResource($user->fresh()->load('membershipLevel'))
        ], 200);
    }

    /**
     * Remove the specified user
     *
     * @param User $user
     * @return JsonResponse
     */
    public function destroy(User $user): JsonResponse
    {
        Gate::authorize('delete', $user);

        // Revoke all tokens before deletion
        $user->tokens()->delete();
        $user->delete();

        return response()->json([
            'message' => 'User deleted successfully'
        ], 200);
    }

    /**
     * Get current authenticated user profile
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function profile(Request $request): JsonResponse
    {
        return response()->json([
            'message' => 'Profile retrieved successfully',
            'data' => new UserResource($request->user()->load('membershipLevel'))
        ], 200);
    }

    /**
     * Update current user profile (name, email, phone)
     *
     * @param UpdateProfileRequest $request
     * @return JsonResponse
     */
    public function updateProfile(UpdateProfileRequest $request): JsonResponse
    {
        $user = $request->user();
        $validated = $request->validated();
        // Log::info('User: ' . $user->id);
        // Log::info('Validated Data: ');
        // Log::info($validated);

        $user->update($validated);

        return response()->json([
            'message' => 'Profile updated successfully',
            'data' => new UserResource($user->fresh()->load('membershipLevel'))
        ], 200);
    }

    /**
     * Change user password
     *
     * @param ChangePasswordRequest $request
     * @return JsonResponse
     */
    public function changePassword(ChangePasswordRequest $request): JsonResponse
    {
        $user = $request->user();
        $validated = $request->validated();

        // Check current password (still need Hash::check for verification)
        if (!Hash::check($validated['current_password'], $user->password)) {
            return response()->json([
                'message' => 'Current password is incorrect',
                'errors' => [
                    'current_password' => [
                        'Current password is incorrect'
                    ]
                ]
            ], 422);
        }

        // Update password (automatically hashed by model cast)
        $user->update([
            'password' => $validated['new_password']
        ]);

        // Optionally revoke all other tokens to force re-login on other devices
        // $user->tokens()->where('id', '!=', $user->currentAccessToken()->id)->delete();

        return response()->json([
            'message' => 'Password changed successfully'
        ], 200);
    }

    /**
     * Get resellers for dropdown/selection (Admin only)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getResellers(Request $request): JsonResponse
    {
        Gate::authorize('viewAny', User::class);

        $query = User::where('role', User::ROLE_RESELLER)
                    ->select('id', 'name', 'email');

        // Search functionality for reseller selection
        if ($request->has('search') && !empty($request->search)) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                ->orWhere('email', 'like', "%{$request->search}%");
            });
        }

        $resellers = $query->orderBy('name', 'asc')->get();

        return response()->json([
            'message' => 'Resellers retrieved successfully',
            'data' => $resellers
        ], 200);
    }
}
