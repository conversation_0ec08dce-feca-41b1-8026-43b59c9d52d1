<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Payment;
use App\Http\Requests\Payment\IndexPaymentRequest;
use App\Http\Resources\Payment\IndexPaymentResource;
use App\Http\Resources\Payment\PaymentResource;
use App\Services\XenditService;
use App\Services\CsvExportService;
use App\Http\Requests\Payment\ExportPaymentRequest;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\Gate;

class PaymentController extends Controller
{
    protected $csvExportService;

    public function __construct(CsvExportService $csvExportService)
    {
        $this->csvExportService = $csvExportService;
    }

    public function index(IndexPaymentRequest $request)
    {

        Gate::authorize('viewAny', Payment::class);

        $query = Payment::query();

        // Filtering
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('order_id')) {
            $query->where('order_id', $request->order_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('min_amount')) {
            $query->where('amount', '>=', $request->min_amount);
        }

        if ($request->filled('max_amount')) {
            $query->where('amount', '<=', $request->max_amount);
        }

        // Searching
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('external_id', 'like', "%{$search}%")
                    ->orWhere('xendit_id', 'like', "%{$search}%")
                    ->orWhere('payment_method', 'like', "%{$search}%")
                    ->orWhere('payment_destination', 'like', "%{$search}%")
                    ->orWhereHas('order', function ($orderQuery) use ($search) {
                        $orderQuery->where('user_name', 'like', "%{$search}%");
                    });
            });
        }

        // Sorting
        $query->orderBy($request->sort_by, $request->sort_direction);

        $payments = $query->with('order')->paginate($request->per_page);

        return IndexPaymentResource::collection($payments);
    }

    public function show(Payment $payment)
    {
        Gate::authorize('view', $payment);
        $payment->load('order');
        return new PaymentResource($payment);
    }
    public function exportCsv(ExportPaymentRequest $request): StreamedResponse
    {
        $validated = $request->validated();

        $query = Payment::query();

        // Apply date range filter
        if (isset($validated['date_from'])) {
            $query->whereDate('created_at', '>=', $validated['date_from']);
        }

        if (isset($validated['date_to'])) {
            $query->whereDate('created_at', '<=', $validated['date_to']);
        }

        // Apply status filter
        if (isset($validated['status'])) {
            $query->where('status', $validated['status']);
        }

        $headings = [
            'ID',
            'Order ID',
            'External ID',
            'Xendit ID',
            'Status',
            'Payment Method',
            'Payment Channel',
            'Payment Destination',
            'Amount',
            'Paid Amount',
            'Currency',
            'Invoice URL',
            'Paid At',
            'Expires At',
            'Failure Reason',
            'Created At',
            'Updated At',
        ];

        $filename = 'payments-export-' . now()->format('Y-m-d-His') . '.csv';

        return $this->csvExportService->streamCollection(
            $query,
            $filename,
            $headings,
            function (Payment $payment) {
                return [
                    'ID' => $payment->id,
                    'Order ID' => $payment->order_id,
                    'External ID' => $payment->external_id,
                    'Xendit ID' => $payment->xendit_id,
                    'Status' => $payment->status,
                    'Payment Method' => $payment->payment_method,
                    'Payment Channel' => $payment->payment_channel,
                    'Payment Destination' => $payment->payment_destination,
                    'Amount' => $payment->amount,
                    'Paid Amount' => $payment->paid_amount,
                    'Currency' => $payment->currency,
                    'Invoice URL' => $payment->invoice_url,
                    'Paid At' => $payment->paid_at ? $payment->paid_at->toDateTimeString() : null,
                    'Expires At' => $payment->expires_at ? $payment->expires_at->toDateTimeString() : null,
                    'Failure Reason' => $payment->failure_reason,
                    'Created At' => $payment->created_at->toDateTimeString(),
                    'Updated At' => $payment->updated_at->toDateTimeString(),
                ];
            }
        );
    }
}
