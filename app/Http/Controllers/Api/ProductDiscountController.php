<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ProductDiscount;
use App\Http\Requests\ProductDiscount\IndexProductDiscountRequest;
use App\Http\Requests\ProductDiscount\BatchUpdateProductDiscountRequest;
use App\Http\Requests\ProductDiscount\BatchDestroyProductDiscountRequest;
use App\Http\Requests\ProductDiscount\BatchIndexProductDiscountRequest;
use App\Http\Resources\ProductDiscount\ProductDiscountResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Gate;

class ProductDiscountController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(IndexProductDiscountRequest $request): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', ProductDiscount::class);

        $validated = $request->validated();
        $query = ProductDiscount::query();

        if (!empty($validated['search'])) {
            $query->where(function ($q) use ($validated) {
                $q->where('jubelio_item_group_id', 'like', '%' . $validated['search'] . '%')
                    ->orWhere('name', 'like', '%' . $validated['search'] . '%');
            });
        }

        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        $discounts = $query->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return ProductDiscountResource::collection($discounts);
    }

    /**
     * Store or update a batch of product discounts.
     */
    public function batchUpdate(BatchUpdateProductDiscountRequest $request): JsonResponse
    {
        Gate::authorize('create', ProductDiscount::class);
        Gate::authorize('update', new ProductDiscount());

        $validated = $request->validated();

        $updatedCount = 0;
        $createdCount = 0;

        foreach ($validated['discounts'] as $discountData) {
            $discount = ProductDiscount::updateOrCreate(
                ['jubelio_item_group_id' => $discountData['jubelio_item_group_id']],
                [
                    'name' => $discountData['name'],
                    'max_discount_percentage' => $discountData['max_discount_percentage']
                ]
            );

            if ($discount->wasRecentlyCreated) {
                $createdCount++;
            } else {
                $updatedCount++;
            }
        }

        return response()->json([
            'message' => "Batch update completed successfully. {$createdCount} discounts created, {$updatedCount} discounts updated."
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProductDiscount $productDiscount): JsonResponse
    {
        Gate::authorize('delete', $productDiscount);

        $productDiscount->delete();

        return response()->json(['message' => 'Product discount deleted successfully'], 200);
    }

    /**
     * Display a listing of the resource.
     */
    public function batchIndex(BatchIndexProductDiscountRequest $request): JsonResponse
    {
        Gate::authorize('viewAny', ProductDiscount::class);

        $validated = $request->validated();

        $discounts = ProductDiscount::whereIn('jubelio_item_group_id', $validated['jubelio_item_group_ids'])->get();

        return ProductDiscountResource::collection($discounts)->response();
    }

    /**
     * Remove a batch of product discounts from storage.
     */
    public function batchDestroy(BatchDestroyProductDiscountRequest $request): JsonResponse
    {
        Gate::authorize('delete', new ProductDiscount());

        $validated = $request->validated();
        $idsToDelete = $validated['jubelio_item_group_ids'];

        $deletedCount = ProductDiscount::whereIn('jubelio_item_group_id', $idsToDelete)->delete();

        return response()->json([
            'message' => "Batch delete completed successfully. {$deletedCount} discounts deleted."
        ], 200);
    }
}
