<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\Jubelio;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class JubelioController extends Controller
{
    protected $jubelio;

    public function __construct(Jubelio $jubelio)
    {
        $this->jubelio = $jubelio;
    }

    public function updateSalesOrder(Request $request)
    {
        $validatedData = $request->validate([
            'salesorder_id' => 'required|integer',
            'contact_id' => 'required|integer',
            'customer_name' => 'required|string',
            'transaction_date' => 'required|date',
            'location_id' => 'required|integer',
            'source' => 'required|integer',
            'items' => 'required|array',
            'items.*.salesorder_detail_id' => 'required|integer',
            'items.*.item_id' => 'required|integer',
            'items.*.tax_id' => 'required|integer',
            'items.*.price' => 'required|numeric',
            'items.*.unit' => 'required|string',
            'items.*.qty_in_base' => 'required|numeric',
            'items.*.disc' => 'required|numeric',
            'items.*.disc_amount' => 'required|numeric',
            'items.*.tax_amount' => 'required|numeric',
            'items.*.amount' => 'required|numeric',
            'items.*.location_id' => 'required|integer',
            'items.*.serial_no' => 'nullable|string',
            'items.*.description' => 'nullable|string',
        ]);

        $result = $this->jubelio->createOrUpdateSalesOrder($validatedData);

        if ($result) {
            return response()->json($result);
        }

        return response()->json(['error' => 'Failed to update sales order in Jubelio'], 500);
    }
    public function setReadyToPick(Request $request)
    {
        $validatedData = $request->validate([
            'salesorder_ids' => 'required|array',
            'salesorder_ids.*' => 'integer',
        ]);

        $result = $this->jubelio->setReadyToPick($validatedData['salesorder_ids']);

        if ($result) {
            return response()->json($result);
        }

        return response()->json(['error' => 'Failed to set sales orders as ready to pick in Jubelio'], 500);
    }
}
