<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use App\Http\Requests\Contact\IndexContactRequest;
use App\Http\Requests\Contact\StoreContactRequest;
use App\Http\Requests\Contact\UpdateContactRequest;
use App\Http\Resources\Contact\ContactResource;
use App\Services\Jubelio;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ContactController extends Controller
{
    protected $jubelio;

    public function __construct()
    {
        $this->jubelio = new Jubelio();
    }

    /**
     * Display a listing of the resource.
     */
    public function index(IndexContactRequest $request): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', Contact::class);

        $validated = $request->validated();
        $query = Contact::query();

        // Search functionality
        if (!empty($validated['search'])) {
            $query->where(function ($q) use ($validated) {
                $q->where('name', 'like', "%{$validated['search']}%")
                  ->orWhere('email', 'like', "%{$validated['search']}%")
                  ->orWhere('phone', 'like', "%{$validated['search']}%")
                  ->orWhere('purpose', 'like', "%{$validated['search']}%");
            });
        }

        // Filter by purpose
        if (!empty($validated['purpose'])) {
            $query->where('purpose', $validated['purpose']);
        }

        // Apply sorting
        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        $contacts = $query->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return ContactResource::collection($contacts);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreContactRequest $request): JsonResponse
    {
        Gate::authorize('create', Contact::class);

        $validated = $request->validated();

        // If a purpose is set, ensure it's the only one
        if (!empty($validated['purpose'])) {
            Contact::where('purpose', $validated['purpose'])->update(['purpose' => null]);

            if ($validated['purpose'] === Contact::PURPOSE_RESELLER_DEFAULT) {
                Cache::forget(Contact::CACHE_KEY_RESELLER_DEFAULT_ID);
            }
        }

        // Create the contact in Jubelio first
        $validated['source'] = 'jubelio';
        $jubelioContact = $this->jubelio->createOrUpdateContact($validated);

        if (!$jubelioContact || !isset($jubelioContact['contact_id'])) {
            return response()->json([
                'message' => 'Failed to create contact in Jubelio',
                'error' => 'Could not get a valid response from Jubelio API'
            ], 502); // 502 Bad Gateway is appropriate for upstream API failures
        }

        // Add Jubelio data to our record before saving
        $validated['jubelio_contact_id'] = $jubelioContact['contact_id'];
        $validated['source_data'] = $jubelioContact;

        $contact = Contact::create($validated);

        return response()->json([
            'message' => 'Contact created successfully',
            'data' => new ContactResource($contact)
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Contact $contact): JsonResponse
    {
        Gate::authorize('view', $contact);

        return response()->json([
            'message' => 'Contact retrieved successfully',
            'data' => new ContactResource($contact)
        ], 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateContactRequest $request, Contact $contact): JsonResponse
    {
        Gate::authorize('update', $contact);

        $validated = $request->validated();

        // If a purpose is set, ensure it's the only one
        if (!empty($validated['purpose'])) {
            // Clear any other contact with the same purpose before setting it on the current one
            Contact::where('purpose', $validated['purpose'])->where('id', '!=', $contact->id)->update(['purpose' => null]);

            if ($validated['purpose'] === Contact::PURPOSE_RESELLER_DEFAULT) {
                Cache::forget(Contact::CACHE_KEY_RESELLER_DEFAULT_ID);
            }
        }

        // Determine which fields are relevant to Jubelio
        $jubelioFields = ['name', 'phone', 'email'];
        $fieldsToUpdateInJubelio = array_intersect_key($validated, array_flip($jubelioFields));

        // Only call the Jubelio API if there are relevant fields to update
        if (!empty($fieldsToUpdateInJubelio)) {
            $jubelioContact = $this->jubelio->createOrUpdateContact($fieldsToUpdateInJubelio, $contact->jubelio_contact_id);

            if (!$jubelioContact) {
                return response()->json([
                    'message' => 'Failed to update contact in Jubelio',
                    'error' => 'Could not get a valid response from Jubelio API'
                ], 502);
            }

            // We don't update source_data on updates, as it's meant to hold the original
            // payload from creation. The successful API call is sufficient.
        }

        $contact->update($validated);

        return response()->json([
            'message' => 'Contact updated successfully',
            'data' => new ContactResource($contact->fresh())
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Contact $contact): JsonResponse
    {
        Gate::authorize('delete', $contact);

        // Delete from Jubelio first
        if ($contact->jubelio_contact_id) {
            $jubelioResult = $this->jubelio->deleteContact($contact->jubelio_contact_id);
            if ($jubelioResult === null) {
                return response()->json([
                    'message' => 'Failed to delete contact in Jubelio. Aborting deletion.',
                    'error' => 'Could not delete contact from Jubelio API.'
                ], 502);
            }
        }

        $contact->delete();

        return response()->json([
            'message' => 'Contact deleted successfully'
        ], 200);
    }
}
