<?php

namespace App\Http\Controllers\Api;

use App\Events\Jubelio\JubelioOrderStatus;
use App\Events\Xendit\XenditInvoiceExpired;
use App\Events\Xendit\XenditInvoicePaid;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Services\Jubelio;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    public function handleXenditInvoiceWebhook(Request $request): JsonResponse
    {
        $xenditWebhookToken = $request->header('x-callback-token');
        $data = $request->all();
        Log::info('Xendit Webhook Data: ', $data);

        if ($xenditWebhookToken !== config('xendit.webhook_token')) {
            return response()->json(['message' => 'Invalid webhook token'], 403);
        }

        // Handle different webhook statuses
        if (isset($data['status'])) {
            switch ($data['status']) {
                case 'PAID':
                    XenditInvoicePaid::dispatch($data);
                    break;
                case 'EXPIRED':
                    XenditInvoiceExpired::dispatch($data);
                    break;
                default:
                    Log::info('Received Xendit webhook with unhandled status.', [
                        'status' => $data['status'],
                        'data' => $data
                    ]);
                    break;
            }
        } else {
            Log::info('Received Xendit webhook with no status.', [
                'data' => $data
            ]);
        }

        return response()->json(['message' => 'Webhook received successfully'], 200);
    }

    public function handleJubelioInvoiceWebhook(Request $request, Jubelio $jubelio): JsonResponse
    {
        $data = $request->all();
        Log::info('Jubelio Webhook Data: ', $data);

        if (!$jubelio->verifyWebhookSignature($request)) {
            return response()->json(['message' => 'Invalid signature. Webhook ignored.'], 403);
        }

        // Filter by online shop name, you can change Otoresell with your store name
        if (!isset($data['store_name']) || $data['store_name'] !== 'Otoresell') {
            Log::info('Jubelio webhook ignored: store_name is not Otoresell.', ['store_name' => $data['store_name'] ?? 'N/A']);
            return response()->json(['message' => 'Webhook ignored: store name is not Otoresell'], 200);
        }

        if (!isset($data['salesorder_id'])) {
            Log::warning('Jubelio webhook: salesorder_id not found in payload.');
            return response()->json(['message' => 'Webhook ignored: salesorder_id not found'], 200);
        }

        $order = Order::where('jubelio_order_id', $data['salesorder_id'])->first();

        if (!$order) {
            Log::warning('Jubelio webhook: order not found.', ['salesorder_id' => $data['salesorder_id']]);
            return response()->json(['message' => 'Webhook ignored: order not found'], 200);
        }

        JubelioOrderStatus::dispatch($order, $data);

        return response()->json(['message' => 'Webhook received successfully'], 200);
    }
}
