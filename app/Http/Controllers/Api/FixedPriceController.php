<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\FixedPrice;
use App\Http\Requests\FixedPrice\IndexFixedPriceRequest;
use App\Http\Requests\FixedPrice\BatchUpdateFixedPriceRequest;
use App\Http\Requests\FixedPrice\BatchDestroyFixedPriceRequest;
use App\Http\Requests\FixedPrice\BatchIndexFixedPriceRequest;
use App\Http\Resources\FixedPrice\FixedPriceResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Gate;

class FixedPriceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(IndexFixedPriceRequest $request): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', FixedPrice::class);

        $validated = $request->validated();
        $query = FixedPrice::query();

        if (!empty($validated['search'])) {
            $query->where(function ($q) use ($validated) {
                $q->where('name', 'like', '%' . $validated['search'] . '%')
                    ->orWhere('jubelio_item_id', 'like', '%' . $validated['search'] . '%')
                    ->orWhere('jubelio_item_group_id', 'like', '%' . $validated['search'] . '%');
            });
        }

        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        $fixedPrices = $query->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return FixedPriceResource::collection($fixedPrices);
    }

    /**
     * Store or update a batch of fixed prices.
     */
    public function batchUpdate(BatchUpdateFixedPriceRequest $request): JsonResponse
    {
        Gate::authorize('create', FixedPrice::class);
        Gate::authorize('update', new FixedPrice());

        $validated = $request->validated();

        $updatedCount = 0;
        $createdCount = 0;

        foreach ($validated['prices'] as $priceData) {
            $fixedPrice = FixedPrice::updateOrCreate(
                ['jubelio_item_id' => $priceData['jubelio_item_id']],
                [
                    'jubelio_item_group_id' => $priceData['jubelio_item_group_id'],
                    'name' => $priceData['name'],
                    'variant' => $priceData['variant'],
                    'price' => $priceData['price'],
                ]
            );

            if ($fixedPrice->wasRecentlyCreated) {
                $createdCount++;
            } else {
                $updatedCount++;
            }
        }

        return response()->json([
            'message' => "Batch update completed successfully. {$createdCount} prices created, {$updatedCount} prices updated."
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FixedPrice $fixedPrice): JsonResponse
    {
        Gate::authorize('delete', $fixedPrice);

        $fixedPrice->delete();

        return response()->json(['message' => 'Fixed price deleted successfully'], 200);
    }

    /**
     * Display a listing of the resource by specific IDs.
     */
    public function batchIndex(BatchIndexFixedPriceRequest $request): JsonResponse
    {
        Gate::authorize('viewAny', FixedPrice::class);

        $validated = $request->validated();

        $fixedPrices = FixedPrice::whereIn('jubelio_item_id', $validated['jubelio_item_ids'])->get();

        return FixedPriceResource::collection($fixedPrices)->response();
    }

    /**
     * Remove a batch of fixed prices from storage.
     */
    public function batchDestroy(BatchDestroyFixedPriceRequest $request): JsonResponse
    {
        Gate::authorize('delete', new FixedPrice());

        $validated = $request->validated();
        $idsToDelete = $validated['jubelio_item_ids'];

        $deletedCount = FixedPrice::whereIn('jubelio_item_id', $idsToDelete)->delete();

        return response()->json([
            'message' => "Batch delete completed successfully. {$deletedCount} prices deleted."
        ], 200);
    }
}
