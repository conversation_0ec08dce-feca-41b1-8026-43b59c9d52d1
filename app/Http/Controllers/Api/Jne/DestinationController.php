<?php

namespace App\Http\Controllers\Api\Jne;

use App\Http\Controllers\Controller;
use App\Http\Requests\Jne\Destination\IndexRequest;
use App\Http\Requests\Jne\Destination\StoreRequest;
use App\Http\Requests\Jne\Destination\UpdateRequest;
use App\Http\Resources\Jne\DestinationResource;
use App\Models\JneDestination;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Gate;

class DestinationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(IndexRequest $request): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', JneDestination::class);

        $validated = $request->validated();

        $query = JneDestination::query();

        // Filter by specific columns
        $filterable = [
            'country_name', 'province_name', 'city_name', 'district_name',
            'subdistrict_name', 'zip_code', 'tariff_code'
        ];

        foreach ($filterable as $filter) {
            if (!empty($validated[$filter])) {
                $query->where($filter, 'like', "%{$validated[$filter]}%");
            }
        }

        // Search functionality
        if (!empty($validated['search'])) {
            $query->where(function ($q) use ($validated) {
                $q->where('country_name', 'like', "%{$validated['search']}%")
                    ->orWhere('province_name', 'like', "%{$validated['search']}%")
                    ->orWhere('city_name', 'like', "%{$validated['search']}%")
                    ->orWhere('district_name', 'like', "%{$validated['search']}%")
                    ->orWhere('subdistrict_name', 'like', "%{$validated['search']}%")
                    ->orWhere('zip_code', 'like', "%{$validated['search']}%")
                    ->orWhere('tariff_code', 'like', "%{$validated['search']}%");
            });
        }

        // Apply sorting
        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        $destinations = $query->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return DestinationResource::collection($destinations);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRequest $request): JsonResponse
    {
        Gate::authorize('create', JneDestination::class);

        $destination = JneDestination::create($request->validated());

        return response()->json([
            'message' => 'JNE Destination created successfully',
            'data' => new DestinationResource($destination)
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(JneDestination $jneDestination): JsonResponse
    {
        Gate::authorize('view', $jneDestination);

        return response()->json([
            'message' => 'JNE Destination retrieved successfully',
            'data' => new DestinationResource($jneDestination)
        ], 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRequest $request, JneDestination $jneDestination): JsonResponse
    {
        Gate::authorize('update', $jneDestination);

        $jneDestination->update($request->validated());

        return response()->json([
            'message' => 'JNE Destination updated successfully',
            'data' => new DestinationResource($jneDestination->fresh())
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(JneDestination $jneDestination): JsonResponse
    {
        Gate::authorize('delete', $jneDestination);

        $jneDestination->delete();

        return response()->json([
            'message' => 'JNE Destination deleted successfully'
        ], 200);
    }
}
