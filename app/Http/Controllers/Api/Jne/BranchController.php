<?php

namespace App\Http\Controllers\Api\Jne;

use App\Http\Controllers\Controller;
use App\Http\Requests\Jne\Branch\IndexRequest;
use App\Http\Requests\Jne\Branch\StoreRequest;
use App\Http\Requests\Jne\Branch\UpdateRequest;
use App\Http\Resources\Jne\BranchResource;
use App\Models\JneBranch;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;

class BranchController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(IndexRequest $request): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', JneBranch::class);

        $validated = $request->validated();

        $query = JneBranch::query();

        // Search functionality
        if (!empty($validated['search'])) {
            $query->where(function ($q) use ($validated) {
                $q->where('name', 'like', "%{$validated['search']}%")
                  ->orWhere('branch_code', 'like', "%{$validated['search']}%");
            });
        }

        // Filter by is_default
        if (isset($validated['is_default'])) {
            $query->where('is_default', $validated['is_default']);
        }

        // Filter by branch_code
        if (isset($validated['branch_code'])) {
            $query->where('branch_code', $validated['branch_code']);
        }

        // Filter by name
        if (isset($validated['name'])) {
            $query->where('name', 'like', "%{$validated['name']}%");
        }

        // Apply sorting
        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        $branches = $query->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return BranchResource::collection($branches);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRequest $request): JsonResponse
    {
        Gate::authorize('create', JneBranch::class);

        $validated = $request->validated();
        // Log::info('Validated data: ' . json_encode($validated));

        if (isset($validated['is_default']) && $validated['is_default']) {
            JneBranch::where('is_default', true)->update(['is_default' => false]);
        }

        $branch = JneBranch::create($validated);

        return response()->json([
            'message' => 'JNE Branch created successfully',
            'data' => new BranchResource($branch)
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(JneBranch $jneBranch): JsonResponse
    {
        Gate::authorize('view', $jneBranch);

        return response()->json([
            'message' => 'JNE Branch retrieved successfully',
            'data' => new BranchResource($jneBranch)
        ], 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRequest $request, JneBranch $jneBranch): JsonResponse
    {
        Gate::authorize('update', $jneBranch);

        $validated = $request->validated();
        // Log::info('Validated data: ' . json_encode($validated));

        if (isset($validated['is_default']) && $validated['is_default']) {
            JneBranch::where('id', '!=', $jneBranch->id)
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        $jneBranch->update($validated);

        return response()->json([
            'message' => 'JNE Branch updated successfully',
            'data' => new BranchResource($jneBranch->fresh())
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(JneBranch $jneBranch): JsonResponse
    {
        Gate::authorize('delete', $jneBranch);

        $jneBranch->delete();

        return response()->json([
            'message' => 'JNE Branch deleted successfully'
        ], 200);
    }
}
