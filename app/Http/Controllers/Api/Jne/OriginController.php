<?php

namespace App\Http\Controllers\Api\Jne;

use App\Http\Controllers\Controller;
use App\Http\Requests\Jne\Origin\IndexRequest;
use App\Http\Requests\Jne\Origin\StoreRequest;
use App\Http\Requests\Jne\Origin\UpdateRequest;
use App\Http\Resources\Jne\OriginResource;
use App\Models\JneOrigin;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;

class OriginController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(IndexRequest $request): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', JneOrigin::class);

        $validated = $request->validated();

        $query = JneOrigin::query();

        // Search functionality
        if (!empty($validated['search'])) {
            $query->where(function ($q) use ($validated) {
                $q->where('name', 'like', "%{$validated['search']}%")
                    ->orWhere('origin_code', 'like', "%{$validated['search']}%");
            });
        }

        // Filter by is_default
        if (isset($validated['is_default'])) {
            $query->where('is_default', $validated['is_default']);
        }

        // Filter by origin_code
        if (isset($validated['origin_code'])) {
            $query->where('origin_code', $validated['origin_code']);
        }

        // Filter by name
        if (isset($validated['name'])) {
            $query->where('name', 'like', "%{$validated['name']}%");
        }

        // Apply sorting
        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        $origins = $query->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return OriginResource::collection($origins);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRequest $request): JsonResponse
    {
        Gate::authorize('create', JneOrigin::class);

        $validated = $request->validated();

        if (isset($validated['is_default']) && $validated['is_default']) {
            JneOrigin::where('is_default', true)->update(['is_default' => false]);
        }

        $origin = JneOrigin::create($validated);

        return response()->json([
            'message' => 'JNE Origin created successfully',
            'data' => new OriginResource($origin)
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(JneOrigin $jneOrigin): JsonResponse
    {
        Gate::authorize('view', $jneOrigin);

        return response()->json([
            'message' => 'JNE Origin retrieved successfully',
            'data' => new OriginResource($jneOrigin)
        ], 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRequest $request, JneOrigin $jneOrigin): JsonResponse
    {
        Gate::authorize('update', $jneOrigin);

        $validated = $request->validated();
        // Log::info('Validated data: ' . json_encode($validated));

        if (isset($validated['is_default']) && $validated['is_default']) {
            JneOrigin::where('id', '!=', $jneOrigin->id)
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        $jneOrigin->update($validated);

        return response()->json([
            'message' => 'JNE Origin updated successfully',
            'data' => new OriginResource($jneOrigin->fresh())
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(JneOrigin $jneOrigin): JsonResponse
    {
        Gate::authorize('delete', $jneOrigin);

        $jneOrigin->delete();

        return response()->json([
            'message' => 'JNE Origin deleted successfully'
        ], 200);
    }
}
