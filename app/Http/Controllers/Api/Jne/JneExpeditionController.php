<?php

namespace App\Http\Controllers\Api\Jne;

use App\Http\Controllers\Controller;
use App\Models\JneBranch;
use App\Models\JneDestination;
use App\Models\JneOrigin;
use Illuminate\Http\Request;

class JneExpeditionController extends Controller
{
    public function getAllData()
    {
        return response()->json([
            'branches' => JneBranch::all(),
            'origins' => JneOrigin::all(),
            'destinations' => JneDestination::all(),
        ]);
    }

    public function getBranches()
    {
        return response()->json(JneBranch::all());
    }

    public function getOrigins()
    {
        return response()->json(JneOrigin::all());
    }

    public function getDestinations()
    {
        return response()->json(JneDestination::all());
    }
}
