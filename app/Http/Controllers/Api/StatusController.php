<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\FrontendApp;
use Illuminate\Http\JsonResponse;

class StatusController extends Controller
{
    /**
     * Check the maintenance status of a frontend application.
     *
     * @param string $identifier
     * @return \Illuminate\Http\JsonResponse
     */
    public function check(string $identifier): JsonResponse
    {
        $app = FrontendApp::where('identifier', $identifier)->first(['maintenance_status']);

        if (!$app) {
            // Fail safely by returning false if the app identifier is not found.
            return response()->json(['maintenance_status' => false]);
        }

        return response()->json([
            'maintenance_status' => $app->maintenance_status
        ]);
    }
}
