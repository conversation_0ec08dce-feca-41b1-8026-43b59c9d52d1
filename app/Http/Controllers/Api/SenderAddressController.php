<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\SenderAddress\IndexSenderAddressRequest;
use App\Http\Requests\SenderAddress\StoreSenderAddressRequest;
use App\Http\Requests\SenderAddress\UpdateSenderAddressRequest;
use App\Http\Resources\SenderAddress\SenderAddressResource;
use App\Models\SenderAddress;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;

class SenderAddressController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(IndexSenderAddressRequest $request): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', SenderAddress::class);

        $user = Auth::user();
        $validated = $request->validated();
        // Log::info('Validated data: ' . json_encode($validated));

        $query = SenderAddress::with('user');

        // Apply user-based filtering
        if (!$user->isAdmin()) {
            // Resellers can only see their own addresses
            $query->byUser($user->id);
        } elseif ($user->isAdmin()) {
            // Admins can see all addresses or filter by specific user
            if (isset($validated['user_id'])) {
                $query->byUser($validated['user_id']);
            }
        }

        // Apply search filter - THIS ALREADY WORKS FOR RESELLERS
        if (isset($validated['search'])) {
            $query->search($validated['search']);
        }

        // Apply is_default filter
        if (isset($validated['is_default']) && $validated['is_default']) {
            $query->default();
        }

        // Apply sorting
        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        // Get results with pagination
        $senderAddresses = $query->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return SenderAddressResource::collection($senderAddresses);
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSenderAddressRequest $request): JsonResponse
    {
        Gate::authorize('create', SenderAddress::class);

        $validated = $request->validated();
        Log::info('validated',$validated);

        // For resellers, ensure they can only create addresses for themselves
        $user = Auth::user();
        if (!$user->isAdmin()) {
            $validated['user_id'] = $user->id;
        }

        if ($validated['is_default'] ?? false) {
            $user->senderAddresses()->update(['is_default' => false]);
        }

        $senderAddress = SenderAddress::create($validated);
        $senderAddress->load('user');

        return response()->json([
            'message' => 'Sender address created successfully',
            'data' => new SenderAddressResource($senderAddress)
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(SenderAddress $senderAddress): JsonResponse
    {
        Gate::authorize('view', $senderAddress);

        $senderAddress->load('user');

        return response()->json([
            'message' => 'Sender address retrieved successfully',
            'data' => new SenderAddressResource($senderAddress)
        ], 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSenderAddressRequest $request, SenderAddress $senderAddress): JsonResponse
    {
        Gate::authorize('update', $senderAddress);

        $validated = $request->validated();

        // For resellers, ensure they can only update their own addresses
        $user = Auth::user();
        if (!$user->isAdmin()) {
            unset($validated['user_id']); // Prevent changing user_id
        }

        if ($validated['is_default'] ?? false) {
            $user->senderAddresses()->where('id', '!=', $senderAddress->id)->update(['is_default' => false]);
        }

        $senderAddress->update($validated);
        $senderAddress->load('user');

        return response()->json([
            'message' => 'Sender address updated successfully',
            'data' => new SenderAddressResource($senderAddress->fresh())
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SenderAddress $senderAddress): JsonResponse
    {
        Gate::authorize('delete', $senderAddress);

        $senderAddress->delete();

        return response()->json([
            'message' => 'Sender address deleted successfully'
        ], 200);
    }

    /**
     * Remove multiple sender addresses from storage.
     */
    public function destroyMultiple(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:sender_addresses,id'
        ]);

        $user = Auth::user();
        $query = SenderAddress::whereIn('id', $request->ids);

        // Apply user-based filtering for non-admin users
        if (!$user->isAdmin()) {
            $query->byUser(Auth::id());
        }

        $senderAddresses = $query->get();

        // Check authorization for each sender address
        foreach ($senderAddresses as $senderAddress) {
            Gate::authorize('delete', $senderAddress);
        }

        $deletedCount = $query->delete();

        return response()->json([
            'message' => "{$deletedCount} sender addresses deleted successfully"
        ], 200);
    }

}
