<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ShippingAddress\IndexShippingAddressRequest;
use App\Http\Requests\ShippingAddress\StoreShippingAddressRequest;
use App\Http\Requests\ShippingAddress\UpdateShippingAddressRequest;
use App\Http\Resources\ShippingAddress\ShippingAddressResource;
use App\Models\ShippingAddress;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;

class ShippingAddressController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(IndexShippingAddressRequest $request): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', ShippingAddress::class);

        $user = Auth::user();
        $validated = $request->validated();

        $query = ShippingAddress::with('user');

        // Apply user-based filtering
        if (!$user->isAdmin()) {
            // Resellers can only see their own addresses
            $query->byUser($user->id);
        } elseif ($user->isAdmin()) {
            // Admins can see all addresses or filter by specific user
            if (isset($validated['user_id'])) {
                $query->byUser($validated['user_id']);
            }
        }

        // Apply search filter - THIS ALREADY WORKS FOR RESELLERS
        if (isset($validated['search'])) {
            $query->search($validated['search']);
        }

        // Apply sorting
        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        // Get results with pagination
        $shippingAddresses = $query->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return ShippingAddressResource::collection($shippingAddresses);
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreShippingAddressRequest $request): JsonResponse
    {
        Gate::authorize('create', ShippingAddress::class);

        $validated = $request->validated();

        // For resellers, ensure they can only create addresses for themselves
        $user = Auth::user();
        if (!$user->isAdmin()) {
            $validated['user_id'] = $user->id;
        }

        $shippingAddress = ShippingAddress::create($validated);
        $shippingAddress->load('user');

        return response()->json([
            'message' => 'Shipping address created successfully',
            'data' => new ShippingAddressResource($shippingAddress)
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ShippingAddress $shippingAddress): JsonResponse
    {
        // Log::info('Showing shipping address: ' . $shippingAddress->id);
        Gate::authorize('view', $shippingAddress);

        $shippingAddress->load('user');

        return response()->json([
            'message' => 'Shipping address retrieved successfully',
            'data' => new ShippingAddressResource($shippingAddress)
        ], 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateShippingAddressRequest $request, ShippingAddress $shippingAddress): JsonResponse
    {
        Gate::authorize('update', $shippingAddress);

        $validated = $request->validated();

        // For resellers, ensure they can only update their own addresses
        $user = Auth::user();
        if (!$user->isAdmin()) {
            unset($validated['user_id']); // Prevent changing user_id
        }

        $shippingAddress->update($validated);
        $shippingAddress->load('user');

        return response()->json([
            'message' => 'Shipping address updated successfully',
            'data' => new ShippingAddressResource($shippingAddress->fresh())
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ShippingAddress $shippingAddress): JsonResponse
    {
        Gate::authorize('delete', $shippingAddress);

        $shippingAddress->delete();

        return response()->json([
            'message' => 'Shipping address deleted successfully'
        ], 200);
    }

    /**
     * Remove multiple shipping addresses from storage.
     */
    public function destroyMultiple(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:shipping_addresses,id'
        ]);

        $user = Auth::user();
        $query = ShippingAddress::whereIn('id', $request->ids);

        // Apply user-based filtering for non-admin users
        if (!$user->isAdmin()) {
            $query->byUser(Auth::id());
        }

        $shippingAddresses = $query->get();

        // Check authorization for each shipping address
        foreach ($shippingAddresses as $shippingAddress) {
            Gate::authorize('delete', $shippingAddress);
        }

        $deletedCount = $query->delete();

        return response()->json([
            'message' => "{$deletedCount} shipping addresses deleted successfully"
        ], 200);
    }

}
