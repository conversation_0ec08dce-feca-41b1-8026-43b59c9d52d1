<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Cart\IndexCartItemRequest;
use App\Http\Requests\Cart\StoreCartItemRequest;
use App\Http\Requests\Cart\UpdateCartItemRequest;
use App\Http\Resources\Cart\CartItemResource;
use App\Models\CartItem;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;

class CartController extends Controller
{
    public function index(IndexCartItemRequest $request): AnonymousResourceCollection|JsonResponse
    {
        Gate::authorize('viewAny', CartItem::class);

        $validated = $request->validated();
        // Log::info($validated);

        $query = CartItem::query();

        if (!empty($validated['user_id'])) {
            $query->where('user_id', $validated['user_id']);
        }

        $cartItems = $query->orderBy('created_at', 'desc')->get();

        return CartItemResource::collection($cartItems);
    }

    public function store(StoreCartItemRequest $request): JsonResponse
    {
        Gate::authorize('create', CartItem::class);

        $validated = $request->validated();

        // Use updateOrCreate to efficiently handle adding items to the cart.
        // This leverages the unique constraint on ['user_id', 'jubelio_item_id'].
        $cartItem = CartItem::updateOrCreate(
            [
                'user_id' => $validated['user_id'],
                'jubelio_item_id' => $validated['jubelio_item_id'],
            ],
            [
                'jubelio_item_group_id' => $validated['jubelio_item_group_id'],
                'sku' => $validated['sku'],
                'name' => $validated['name'],
                'unit' => $validated['unit'],
                'price' => $validated['price'],
                'quantity' => \DB::raw('quantity + ' . $validated['quantity']),
                'weight' => $validated['weight'],
                'variant' => $validated['variant'],
                'image' => $validated['image'],
                'tax_id' => $validated['tax_id'],
                'tax_rate_percent' => $validated['tax_rate_percent'],
            ]
        );

        // If the item was just created, the quantity would have been set from the raw expression.
        // We need to set it to the initial validated quantity.
        if ($cartItem->wasRecentlyCreated) {
            $cartItem->quantity = $validated['quantity'];
            $cartItem->save();
        }

        $cartItem->refresh(); // Refresh to get the updated quantity from the DB

        return response()->json([
            'message' => $cartItem->wasRecentlyCreated ? 'Item added to cart successfully' : 'Item quantity updated in cart',
            'data' => new CartItemResource($cartItem)
        ], $cartItem->wasRecentlyCreated ? 201 : 200);
    }

    public function show(CartItem $cartItem): JsonResponse
    {
        Gate::authorize('view', $cartItem);

        return response()->json([
            'data' => new CartItemResource($cartItem)
        ]);
    }

    public function update(UpdateCartItemRequest $request, CartItem $cartItem): JsonResponse
    {
        Gate::authorize('update', $cartItem);

        $cartItem->update($request->validated());

        return response()->json([
            'message' => 'Cart item updated successfully',
            'data' => new CartItemResource($cartItem)
        ]);
    }

    public function destroy(CartItem $cartItem): JsonResponse
    {
        Gate::authorize('delete', $cartItem);

        $cartItem->delete();

        return response()->json([
            'message' => 'Item removed from cart successfully'
        ]);
    }

    public function destroyMultiple(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:cart_items,id'
        ]);

        // Log::info($request->ids);

        $deletedCount = CartItem::where('user_id', Auth::id())
            ->whereIn('id', $request->ids)
            ->delete();

        return response()->json([
            'message' => "{$deletedCount} items removed from cart successfully"
        ]);
    }

    public function clear(): JsonResponse
    {
        $deletedCount = CartItem::where('user_id', Auth::id())->delete();

        return response()->json([
            'message' => "Cart cleared successfully. {$deletedCount} items removed."
        ]);
    }

    public function summary(): JsonResponse
    {
        $cartItems = CartItem::where('user_id', Auth::id())->get();

        $summary = [
            'total_items' => $cartItems->count(),
            'total_quantity' => $cartItems->sum('quantity'),
            'total_weight' => $cartItems->sum(function ($item) {
                return $item->weight * $item->quantity;
            }),
            'subtotal' => $cartItems->sum('total_price'),
            'total_tax' => $cartItems->sum('tax_amount'),
            'total_amount' => $cartItems->sum('total_with_tax'),
        ];

        return response()->json([
            'data' => $summary
        ]);
    }
}
