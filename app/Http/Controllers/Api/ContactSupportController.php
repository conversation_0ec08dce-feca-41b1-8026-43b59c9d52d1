<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ContactSupport\IndexContactSupportRequest;
use App\Http\Requests\ContactSupport\PublicIndexContactSupportRequest;
use App\Http\Requests\ContactSupport\StoreContactSupportRequest;
use App\Http\Requests\ContactSupport\UpdateContactSupportRequest;
use App\Http\Resources\ContactSupport\ContactSupportResource;
use App\Models\ContactSupport;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;

class ContactSupportController extends Controller
{
    /**
     * Display a listing of the resource for admin.
     */
    public function index(IndexContactSupportRequest $request): AnonymousResourceCollection
    {
        Gate::authorize('viewAny', ContactSupport::class);

        $validated = $request->validated();

        $query = ContactSupport::query();

        if (!empty($validated['search'])) {
            $query->where(function ($q) use ($validated) {
                $q->where('name', 'like', "%{$validated['search']}%")
                  ->orWhere('category', 'like', "%{$validated['search']}%")
                  ->orWhere('type', 'like', "%{$validated['search']}%")
                  ->orWhere('value', 'like', "%{$validated['search']}%");
            });
        }

        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        $contactSupports = $query->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return ContactSupportResource::collection($contactSupports);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreContactSupportRequest $request): JsonResponse
    {
        Gate::authorize('create', ContactSupport::class);

        $contactSupport = ContactSupport::create($request->validated());

        return response()->json([
            'message' => 'Support contact created successfully',
            'data' => new ContactSupportResource($contactSupport)
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ContactSupport $contactSupport): ContactSupportResource
    {
        Gate::authorize('view', $contactSupport);

        return new ContactSupportResource($contactSupport);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateContactSupportRequest $request, ContactSupport $contactSupport): JsonResponse
    {
        Gate::authorize('update', $contactSupport);

        $contactSupport->update($request->validated());

        return response()->json([
            'message' => 'Support contact updated successfully',
            'data' => new ContactSupportResource($contactSupport)
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ContactSupport $contactSupport): JsonResponse
    {
        Gate::authorize('delete', $contactSupport);

        $contactSupport->delete();

        return response()->json([
            'message' => 'Support contact deleted successfully'
        ]);
    }

    /**
     * Display a public listing of the resource, with optional filtering.
     */
    public function publicIndex(PublicIndexContactSupportRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();
        // Log::info('Validated data: ' . json_encode($validated));

        $query = ContactSupport::where('is_active', true);

        if (isset($validated['type'])) {
            $query->where('type', $validated['type']);
        }

        if (isset($validated['category'])) {
            $query->where('category', $validated['category']);
        }

        if (isset($validated['limit'])) {
            $query->limit($validated['limit']);
        }

        $contacts = $query->orderBy('category')->orderBy('name')->get();
        Log::info('Contacts: ' . json_encode($contacts));

        return ContactSupportResource::collection($contacts);
    }
}
