<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\MembershipLevel\IndexMembershipLevelRequest;
use App\Http\Requests\MembershipLevel\StoreMembershipLevelRequest;
use App\Http\Requests\MembershipLevel\UpdateMembershipLevelRequest;
use App\Http\Resources\MembershipLevel\MembershipLevelResource;
use App\Models\MembershipLevel;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Gate;

class MembershipLevelController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(IndexMembershipLevelRequest $request)
    {
        Gate::authorize('viewAny', MembershipLevel::class);

        $validated = $request->validated();

        $query = MembershipLevel::query();

        if (isset($validated['search'])) {
            $query->where('name', 'like', '%' . $validated['search'] . '%');
        }

        if (isset($validated['sort_by']) && isset($validated['sort_direction'])) {
            $query->orderBy($validated['sort_by'], $validated['sort_direction']);
        }

        $perPage = $validated['per_page'] ?? 15;

        return MembershipLevelResource::collection($query->paginate($perPage));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreMembershipLevelRequest $request)
    {
        Gate::authorize('create', MembershipLevel::class);

        $validated = $request->validated();

        if (isset($validated['is_default']) && $validated['is_default']) {
            MembershipLevel::where('is_default', true)->update(['is_default' => false]);
        }

        $membershipLevel = MembershipLevel::create($validated);

        return response()->json([
            'message' => 'Membership level created successfully',
            'data' => new MembershipLevelResource($membershipLevel)
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified resource.
     */
    public function show(MembershipLevel $membershipLevel)
    {
        Gate::authorize('view', $membershipLevel);

        return response()->json([
            'message' => 'Membership level retrieved successfully',
            'data' => new MembershipLevelResource($membershipLevel)
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateMembershipLevelRequest $request, MembershipLevel $membershipLevel)
    {
        Gate::authorize('update', $membershipLevel);

        $validated = $request->validated();

        if (isset($validated['is_default']) && $validated['is_default']) {
            MembershipLevel::where('id', '!=', $membershipLevel->id)
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        $membershipLevel->update($validated);

        return response()->json([
            'message' => 'Membership level updated successfully',
            'data' => new MembershipLevelResource($membershipLevel)
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MembershipLevel $membershipLevel)
    {
        Gate::authorize('delete', $membershipLevel);

        if ($membershipLevel->is_default) {
            return response()->json(['message' => 'Cannot delete the default membership level.'], Response::HTTP_FORBIDDEN);
        }

        if ($membershipLevel->users()->exists()) {
            return response()->json(['message' => 'Cannot delete a membership level that is associated with users.'], Response::HTTP_FORBIDDEN);
        }

        $membershipLevel->delete();

        return response()->json(['message' => 'Membership level deleted successfully']);
    }
}
