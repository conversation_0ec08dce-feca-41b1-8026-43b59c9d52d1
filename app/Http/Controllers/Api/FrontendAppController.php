<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\FrontendApp;
use App\Models\User;
use App\Http\Requests\FrontendApp\StoreFrontendAppRequest;
use App\Http\Requests\FrontendApp\UpdateFrontendAppRequest;
use App\Http\Requests\FrontendApp\IndexFrontendAppRequest;
use App\Http\Resources\FrontendApp\FrontendAppResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;

class FrontendAppController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(IndexFrontendAppRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();
        $query = FrontendApp::query();

        // Search functionality
        if (!empty($validated['search'])) {
            $query->where(function ($q) use ($validated) {
                $q->where('name', 'like', "%{$validated['search']}%")
                  ->orWhere('identifier', 'like', "%{$validated['search']}%");
            });
        }

        $frontendApps = $query->paginate(
            perPage: $validated['per_page'] ?? 15,
            page: $validated['page'] ?? 1
        );

        return FrontendAppResource::collection($frontendApps);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreFrontendAppRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $frontendApp = FrontendApp::create($validated);

        // If auto_assign_to_role is set, sync the app with all users of that role.
        if (!empty($validated['auto_assign_to_role'])) {
            $users = User::where('role', $validated['auto_assign_to_role'])->get();
            foreach ($users as $user) {
                $user->frontendApps()->syncWithoutDetaching([$frontendApp->id]);
            }
        }

        return response()->json([
            'message' => 'Frontend App created successfully',
            'data' => new FrontendAppResource($frontendApp)
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(FrontendApp $frontendApp): JsonResponse
    {
        Gate::authorize('view', $frontendApp);

        return response()->json([
            'message' => 'Frontend App retrieved successfully',
            'data' => new FrontendAppResource($frontendApp)
        ], 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateFrontendAppRequest $request, FrontendApp $frontendApp): JsonResponse
    {
        $validated = $request->validated();

        // If maintenance mode is being enabled, set the force_logout_before timestamp.
        if (isset($validated['maintenance_status']) && $validated['maintenance_status'] === true) {
            $validated['force_logout_before'] = Carbon::now();
        }

        $oldRole = $frontendApp->auto_assign_to_role;
        $newRole = $validated['auto_assign_to_role'] ?? null;

        // If the role is changed, update user permissions accordingly.
        if ($oldRole !== $newRole) {
            // Revoke access from users with the old role.
            if (!empty($oldRole)) {
                $usersWithOldRole = User::where('role', $oldRole)->get();
                foreach ($usersWithOldRole as $user) {
                    $user->frontendApps()->detach($frontendApp->id);
                }
            }

            // Grant access to users with the new role.
            if (!empty($newRole)) {
                $usersWithNewRole = User::where('role', $newRole)->get();
                foreach ($usersWithNewRole as $user) {
                    $user->frontendApps()->syncWithoutDetaching([$frontendApp->id]);
                }
            }
        }

        $frontendApp->update($validated);

        return response()->json([
            'message' => 'Frontend App updated successfully',
            'data' => new FrontendAppResource($frontendApp->fresh())
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FrontendApp $frontendApp): JsonResponse
    {
        Gate::authorize('delete', $frontendApp);

        $frontendApp->delete();

        return response()->json([
            'message' => 'Frontend App deleted successfully'
        ], 200);
    }
}
