<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginPostRequest;
use App\Http\Requests\Auth\LogoutPostRequest;
use App\Http\Requests\Auth\RegisterRequest;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use App\Http\Resources\User\UserResource;
use App\Models\Contact;
use App\Models\FrontendApp;
use App\Models\MembershipLevel;
use App\Models\User;
use App\Notifications\WelcomeNotification;
use App\Services\Jubelio;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\PersonalAccessToken;
use Illuminate\Auth\Events\Registered;
use App\Jobs\CreateJubelioContact;

use Illuminate\Support\Facades\Password;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password as PasswordRule;

class AuthController extends Controller
{
    protected $jubelio;
    public function __construct() {
        $this->jubelio = new Jubelio();
    }

    /**
     * Login user and create token
     *
     * @param LoginPostRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(LoginPostRequest $request)
    {
        // --- 1. VALIDATE ---
        // Validate request body
        $credentials = $request->validated();

        // Validate X-App-Identifier Header
        $identifier = $request->header('X-App-Identifier');
        if (empty($identifier)) {
            return response()->json(['message' => 'The application identifier is missing. Please contact support.'], 400);
        }

        $app = FrontendApp::where('identifier', $identifier)->first();
        if (!$app) {
            return response()->json(['message' => 'The application identifier is invalid. Please contact support.'], 401);
        }

        // --- 2. AUTHENTICATE ---
        if (!Auth::attempt($credentials)) {
            return response()->json(['message' => 'The email or password you entered is incorrect. Please try again.'], 401);
        }

        /** @var User $user */
        $user = Auth::user();

        if (!$user->hasVerifiedEmail()) {
            Auth::logout(); // Log them out immediately
            return response()->json([
                'message' => 'Your email address is not verified. Please check your email for a verification link.'
            ], 403);
        }

        // --- NEW CHECK ---
        // Add this check right after email verification
        if (!$user->isAdmin() && !$user->is_active) {
            Auth::logout(); // Log them out immediately
            return response()->json([
                'code' => 'ACCOUNT_INACTIVE',
                'message' => 'Your account is pending activation. Please wait for an administrator to approve your account.'
            ], 403); // 403 Forbidden is a good status code here.
        }

        // --- 3. AUTHORIZE ---
        // Check if the user has permission for this specific app
        if (!$user->frontendApps()->where('frontend_app_id', $app->id)->exists()) {
            Auth::logout(); // Log out the user as they are not authorized for this app
            return response()->json(['message' => 'You do not have permission to access this application.'], 403);
        }

        // Check if the app is in maintenance mode
        if ($app->maintenance_status === true) {
             Auth::logout();
             return response()->json(['message' => 'This application is currently down for maintenance. Please try again later.'], 503);
        }


        // --- TOKEN GENERATION ---
        $tokenName = $user->isAdmin() ? 'otoadmin-' . $user->id : 'otoresell-' . $user->id;
        $token = $user->createToken($tokenName, ['*'], now()->addDay())->plainTextToken;

        $jubelioToken = $this->jubelio->getToken();

        return response()->json([
            'user' => new UserResource($user->load('membershipLevel')),
            'token_type' => 'Bearer',
            'token' => $token,
            'jubelio_token' => $jubelioToken,
            'message' => 'Logged in successfully'
        ], 200);
    }

    /**
     * Logout user from current device
     *
     * @param LogoutPostRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(LogoutPostRequest $request)
    {
        // Get the current access token
        /** @var PersonalAccessToken|null $token */
        $token = $request->user()->currentAccessToken();

        // Delete the token if it exists (for API token authentication)
        if ($token) {
            $token->delete();
        }

        return response()->json([
            'message' => 'Logged out successfully'
        ], 200);
    }

    /**
     * Logout user from all devices
     *
     * @param LogoutPostRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logoutAll(LogoutPostRequest $request)
    {
        $deletedTokens = $request->user()->tokens()->count();
        $request->user()->tokens()->delete();

        return response()->json([
            'message' => 'Logged out from all devices successfully',
            'tokens_revoked' => $deletedTokens
        ], 200);
    }

    /**
     * get jubelio token
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getJubelioToken(Request $request)
    {
        $jubelioToken = $this->jubelio->loginToGetToken();

        if ($jubelioToken === null) {
            return response()->json([
                'message' => 'We are currently unable to connect to our partner service. Please try again later.'
            ], 500);
        }

        return response()->json([
            'message' => 'Jubelio token retrieved successfully',
            'jubelio_token' => $jubelioToken
        ], 200);
    }

    public function register(RegisterRequest $request)
    {
        // --- NEW HEALTH CHECK ---
        $defaultMembershipLevel = MembershipLevel::where('is_default', true)->first();

        if (! $defaultMembershipLevel) {
            // Log the critical error for developers
            Log::critical('System configuration error: Default Membership Level not found. Registration is blocked.');

            // Return a 503 Service Unavailable response to the user
            return response()->json([
                'message' => 'We are currently experiencing a technical issue with our services. Please try again in a few minutes. If the problem continues, please contact support.'
            ], 503);
        }
        // --- END HEALTH CHECK ---

        // Pre-check: Ensure at least one FrontendApp is configured for the 'reseller' role.
        $appExistsForReseller = FrontendApp::where('auto_assign_to_role', User::ROLE_RESELLER)->exists();

        if (!$appExistsForReseller) {
            return response()->json([
                'message' => 'User registration is currently disabled. Please try again later or contact support.',
            ], 503);
        }

        $validatedData = $request->validated();
        $validatedData['role'] = User::ROLE_RESELLER;
        $validatedData['membership_level_id'] = $defaultMembershipLevel->id;

        $user = User::create($validatedData);

        // Automatically assign all apps that are configured for the 'reseller' role.
        $appsToAssign = FrontendApp::where('auto_assign_to_role', User::ROLE_RESELLER)->pluck('id');
        $user->frontendApps()->sync($appsToAssign);

        event(new Registered($user));

        return response()->json([
            'message' => 'Registration successful. Please check your email to verify your account.'
        ], 201);
    }

    public function verifyEmail(Request $request, $id)
    {
        $user = User::findOrFail($id);

        if (! hash_equals((string) $request->route('hash'), sha1($user->getEmailForVerification()))) {
            return response()->json(['message' => 'Invalid verification link.'], 403);
        }

        if ($user->hasVerifiedEmail()) {
            return response()->json(['message' => 'Email already verified.'], 200);
        }

        if ($user->markEmailAsVerified()) {
            event(new \Illuminate\Auth\Events\Verified($user));
            $user->notify(new WelcomeNotification());
        }

        // Dispatch a background job to handle the Jubelio creation
        // CreateJubelioContact::dispatch($user); // Disabled as contacts are now created manually via ContactController

        // if ($user->role === User::ROLE_ADMIN) {
        //     $tokenName = 'otoadmin-' . $user->id;
        // } else {
        //     $tokenName = 'otoresell-' . $user->id;
        // }
        // $token = $user->createToken($tokenName, ['*'], now()->addDay())->plainTextToken;

        // $jubelioToken = $this->jubelio->getToken();

        // return response()->json([
        //     'message' => 'Email verified successfully. You are now logged in.',
        //     'user' => new UserResource($user),
        //     'token_type' => 'Bearer',
        //     'token' => $token,
        //     'jubelio_token' => $jubelioToken,
        // ]);
        return response()->json([
            'message' => 'Email verified successfully. Please contact support to activate your account.'
        ], 200);
    }

    public function resendPublicVerificationEmail(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $user = User::where('email', $request->email)->first();

        if ($user && $user->hasVerifiedEmail()) {
            return response()->json([
                'message' => 'This email address has already been verified.'
            ], 422);
        }

        // IMPORTANT: Only send if the user exists and is NOT already verified.
        if ($user && ! $user->hasVerifiedEmail()) {
            $user->sendEmailVerificationNotification();
        }

        // Always return the same success message to prevent user enumeration.
        return response()->json([
            'message' => 'If an account with this email exists and is not verified, a new verification link has been sent.'
        ], 200);
    }

    public function resendVerificationEmail(Request $request)
    {
        $request->user()->sendEmailVerificationNotification();

        return response()->json([
            'message' => 'A new verification link has been sent to your email address.'
        ]);
    }

    public function forgotPassword(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink($request->only('email'));

        if ($status == Password::RESET_LINK_SENT) {
            return response()->json([
                'message' => __($status)
            ], 200);
        }

        return response()->json(['message' => __($status)], 400);
    }

    public function resetPassword(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => ['required', 'confirmed', PasswordRule::defaults()],
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) {
                $user->forceFill([
                    'password' => Hash::make($password)
                ])->setRememberToken(Str::random(60));

                $user->save();

                event(new PasswordReset($user));
            }
        );

        if ($status === Password::PASSWORD_RESET) {
            return response()->json(['message' => __($status)], 200);
        }

        return response()->json(['message' => __($status)], 400);
    }

    // public function checkActivationStatus(Request $request)
    // {
    //     $request->validate(['email' => 'required|email']);

    //     $user = User::where('email', $request->email)->first();

    //     if (!$user) {
    //         return response()->json(['is_active' => false, 'message' => 'User not found.'], 404);
    //     }

    //     return response()->json(['is_active' => $user->is_active]);
    // }


    // public function user(Request $request)
    // {
    //     return response()->json($request->user());
    // }
}
