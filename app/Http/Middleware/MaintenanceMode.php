<?php

namespace App\Http\Middleware;

use App\Models\FrontendApp;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Lara<PERSON>\Sanctum\PersonalAccessToken;
use Symfony\Component\HttpFoundation\Response;

class MaintenanceMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $appIdentifier = $request->header('X-App-Identifier');

        if (!$appIdentifier) {
            return response()->json(['message' => 'Header X-App-Identifier needed'], 400);
        }

        $app = FrontendApp::where('identifier', $appIdentifier)->first();

        if (!$app) {
            // The identifier sent does not match any known application.
            return response()->json(['message' => 'Invalid application identifier.'], 400);
        }

        // Check 1: Is the application in maintenance mode?
        if ($app->maintenance_status) {
            return response()->json(['message' => 'Application is currently under maintenance.'], 503);
        }

        // Check 2: Does the user need to be forcibly logged out?
        $user = $request->user();
        if ($user && $app->force_logout_before) {
            /** @var PersonalAccessToken|null $token */
            $token = $user->currentAccessToken();
            if ($token->created_at < $app->force_logout_before) {
                $token->delete(); // Invalidate the token
                return response()->json(['message' => 'Session expired. Please log in again.'], 401);
            }
        }

        return $next($request);
    }
}
