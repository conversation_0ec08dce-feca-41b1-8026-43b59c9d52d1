<?php

namespace App\Http\Resources\Settings;

use App\Models\NotificationChannel;
use App\Models\UserNotificationSetting;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\NotificationType
 */
class NotificationTypeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $userNotificationSettings = $this->whenLoaded('userNotificationSettings');

        $setting = $userNotificationSettings?->first();

        if (! $setting) {
            $defaultChannel = NotificationChannel::where('slug', 'mail')->first();
            if ($defaultChannel) {
                $setting = new UserNotificationSetting([
                    'notification_type_id' => $this->id,
                    'notification_channel_id' => $defaultChannel->id,
                ]);
                $setting->setRelation('notificationChannel', $defaultChannel);
            }
        }

        return [
            'id' => $this->id,
            'slug' => $this->slug,
            'name' => $this->name,
            'description' => $this->description,
            'setting' => new UserNotificationSettingResource($setting),
        ];
    }
}
