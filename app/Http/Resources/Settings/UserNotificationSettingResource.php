<?php

namespace App\Http\Resources\Settings;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\UserNotificationSetting
 */
class UserNotificationSettingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'channel' => [
                'id' => $this->resource->notificationChannel->id,
                'slug' => $this->resource->notificationChannel->slug,
                'name' => $this->resource->notificationChannel->name,
            ],
        ];
    }
}
