<?php

namespace App\Http\Resources\Payment;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property-read \App\Models\Payment $resource
 */
class IndexPaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'order_id' => $this->resource->order_id,
            'user_name' => $this->whenLoaded('order', $this->resource->order->user_name),
            'external_id' => $this->resource->external_id,
            'status' => $this->resource->status,
            'payment_method' => $this->resource->payment_method,
            'payment_channel' => $this->resource->payment_channel,
            'amount' => $this->resource->amount,
            'paid_at' => $this->resource->paid_at,
            'created_at' => $this->resource->created_at,
        ];
    }
}
