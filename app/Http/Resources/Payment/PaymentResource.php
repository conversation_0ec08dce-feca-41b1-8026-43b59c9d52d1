<?php

namespace App\Http\Resources\Payment;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property-read \App\Models\Payment $resource
 */
class PaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'order_id' => $this->resource->order_id,
            'external_id' => $this->resource->external_id,
            'user_name' => $this->whenLoaded('order', $this->resource->order->user_name),
            'xendit_id' => $this->resource->xendit_id,
            'invoice_url' => $this->resource->invoice_url,
            'payment_method' => $this->resource->payment_method,
            'payment_channel' => $this->resource->payment_channel,
            'payment_destination' => $this->resource->payment_destination,
            'amount' => $this->resource->amount,
            'paid_amount' => $this->resource->paid_amount,
            'currency' => $this->resource->currency,
            'status' => $this->resource->status,
            'expires_at' => $this->resource->expires_at,
            'paid_at' => $this->resource->paid_at,
            'failure_reason' => $this->resource->failure_reason,
            'xendit_response' => $this->resource->xendit_response,
            'webhook_payload' => $this->resource->webhook_payload,
            'created_at' => $this->resource->created_at,
            'updated_at' => $this->resource->updated_at,
        ];
    }
}
