<?php

namespace App\Http\Resources\FixedPrice;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\FixedPrice
 */
class FixedPriceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'jubelio_item_group_id' => $this->jubelio_item_group_id,
            'jubelio_item_id' => $this->jubelio_item_id,
            'name' => $this->name,
            'variant' => $this->variant,
            'price' => $this->price,
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}
