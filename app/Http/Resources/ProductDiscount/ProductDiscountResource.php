<?php

namespace App\Http\Resources\ProductDiscount;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\ProductDiscount
 */
class ProductDiscountResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'jubelio_item_group_id' => $this->jubelio_item_group_id,
            'name' => $this->name,
            'max_discount_percentage' => $this->max_discount_percentage,
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}
