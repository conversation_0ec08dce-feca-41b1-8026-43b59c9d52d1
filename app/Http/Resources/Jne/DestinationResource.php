<?php

namespace App\Http\Resources\Jne;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\JneDestination
 */
class DestinationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'country_name' => $this->resource->country_name,
            'province_name' => $this->resource->province_name,
            'city_name' => $this->resource->city_name,
            'district_name' => $this->resource->district_name,
            'subdistrict_name' => $this->resource->subdistrict_name,
            'zip_code' => $this->resource->zip_code,
            'tariff_code' => $this->resource->tariff_code,
            'created_at' => $this->resource->created_at,
            'updated_at' => $this->resource->updated_at,
        ];
    }
}
