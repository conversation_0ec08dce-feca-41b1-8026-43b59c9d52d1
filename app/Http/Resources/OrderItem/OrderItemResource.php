<?php

namespace App\Http\Resources\OrderItem;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'jubelio_item_id' => $this->resource->jubelio_item_id,
            'jubelio_item_group_id' => $this->resource->jubelio_item_group_id,
            'sku' => $this->resource->sku,
            'name' => $this->resource->name,
            'unit' => $this->resource->unit,
            'variant' => $this->resource->variant,
            'image' => $this->resource->image,
            'weight' => $this->resource->weight,
            'original_price' => $this->resource->original_price,
            'discount_percentage' => $this->resource->discount_percentage,
            'price' => $this->resource->price,
            'quantity' => $this->resource->quantity,
            'tax_id' => $this->resource->tax_id,
            'tax_rate_percent' => $this->resource->tax_rate_percent,
        ];
    }
}
