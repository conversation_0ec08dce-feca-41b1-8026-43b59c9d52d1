<?php

namespace App\Http\Resources\ShippingAddress;

use App\Http\Resources\User\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ShippingAddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // return parent::toArray($request);

        return [
            'id' => $this->resource->id,
            'shipping_full_name' => $this->resource->shipping_full_name,
            'shipping_phone' => $this->resource->shipping_phone,
            'shipping_address' => $this->resource->shipping_address,
            'shipping_province_id' => $this->resource->shipping_province_id,
            'shipping_province' => $this->resource->shipping_province,
            'shipping_city_id' => $this->resource->shipping_city_id,
            'shipping_city' => $this->resource->shipping_city,
            'shipping_district_id' => $this->resource->shipping_district_id,
            'shipping_district' => $this->resource->shipping_district,
            'shipping_subdistrict_id' => $this->resource->shipping_subdistrict_id,
            'shipping_subdistrict' => $this->resource->shipping_subdistrict,
            'shipping_post_code' => $this->resource->shipping_post_code,
            'user_id' => $this->resource->user_id,
            'user' => new UserResource($this->whenLoaded('user')),
            'created_at' => $this->resource->created_at?->toISOString(),
            'updated_at' => $this->resource->updated_at?->toISOString(),
        ];
    }
}
