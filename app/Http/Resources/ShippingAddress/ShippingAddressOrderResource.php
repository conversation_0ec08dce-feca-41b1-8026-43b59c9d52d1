<?php

namespace App\Http\Resources\ShippingAddress;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ShippingAddressOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'shipping_full_name' => $this->resource->shipping_full_name,
            'shipping_phone' => $this->resource->shipping_phone,
            'shipping_address' => $this->resource->shipping_address,
            'shipping_province_id' => $this->resource->shipping_province_id,
            'shipping_province' => $this->resource->shipping_province,
            'shipping_city_id' => $this->resource->shipping_city_id,
            'shipping_city' => $this->resource->shipping_city,
            'shipping_district_id' => $this->resource->shipping_district_id,
            'shipping_district' => $this->resource->shipping_district,
            'shipping_subdistrict_id' => $this->resource->shipping_subdistrict_id,
            'shipping_subdistrict' => $this->resource->shipping_subdistrict,
            'shipping_post_code' => $this->resource->shipping_post_code,
        ];
    }
}
