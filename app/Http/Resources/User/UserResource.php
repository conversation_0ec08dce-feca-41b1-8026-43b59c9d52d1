<?php

namespace App\Http\Resources\User;

use App\Http\Resources\FrontendApp\FrontendAppResource;
use App\Http\Resources\MembershipLevel\MembershipLevelResource;
use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;

/**
 * @property-read \App\Models\User $resource
 */
class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'email' => $this->resource->email,
            'phone' => $this->resource->phone,
            'role' => $this->resource->role,
            'is_active' => $this->resource->is_active,
            'jubelio_contact_id' => Cache::rememberForever(Contact::CACHE_KEY_RESELLER_DEFAULT_ID, function () {
                return Contact::where('purpose', Contact::PURPOSE_RESELLER_DEFAULT)->value('jubelio_contact_id');
            }),
            'email_verified' => $this->resource->hasVerifiedEmail(),
            'created_at' => $this->resource->created_at,
            'updated_at' => $this->resource->updated_at,
            'frontend_apps' => FrontendAppResource::collection($this->whenLoaded('frontendApps')),
            'membership_level' => new MembershipLevelResource($this->whenLoaded('membershipLevel')),
        ];
    }
}
