<?php

namespace App\Http\Resources\FrontendApp;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FrontendAppResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'identifier' => $this->resource->identifier,
            'auto_assign_to_role' => $this->resource->auto_assign_to_role,
            'maintenance_status' => $this->resource->maintenance_status,
            'force_logout_before' => $this->resource->force_logout_before,
            'created_at' => $this->resource->created_at,
            'updated_at' => $this->resource->updated_at,
        ];
    }
}
