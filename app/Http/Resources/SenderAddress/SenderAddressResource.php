<?php

namespace App\Http\Resources\SenderAddress;

use App\Http\Resources\User\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SenderAddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'sender_full_name' => $this->resource->sender_full_name,
            'sender_phone' => $this->resource->sender_phone,
            'sender_address' => $this->resource->sender_address,
            'sender_province_id' => $this->resource->sender_province_id,
            'sender_province' => $this->resource->sender_province,
            'sender_city_id' => $this->resource->sender_city_id,
            'sender_city' => $this->resource->sender_city,
            'sender_district_id' => $this->resource->sender_district_id,
            'sender_district' => $this->resource->sender_district,
            'sender_subdistrict_id' => $this->resource->sender_subdistrict_id,
            'sender_subdistrict' => $this->resource->sender_subdistrict,
            'sender_post_code' => $this->resource->sender_post_code,
            'is_default' => $this->resource->is_default,
            'user_id' => $this->resource->user_id,
            'user' => new UserResource($this->whenLoaded('user')),
            'created_at' => $this->resource->created_at?->toISOString(),
            'updated_at' => $this->resource->updated_at?->toISOString(),
        ];
    }
}
