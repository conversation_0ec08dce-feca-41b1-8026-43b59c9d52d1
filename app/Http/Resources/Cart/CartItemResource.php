<?php

namespace App\Http\Resources\Cart;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property-read \App\Models\CartItem $resource
 */
class CartItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // return parent::toArray($request);
        return [
            'id' => $this->resource->id,
            'jubelio_item_id' => $this->resource->jubelio_item_id,
            'jubelio_item_group_id' => $this->resource->jubelio_item_group_id,
            'sku' => $this->resource->sku,
            'name' => $this->resource->name,
            'unit' => $this->resource->unit,
            'price' => (float) $this->resource->price,
            'quantity' => $this->resource->quantity,
            'weight' => (float) $this->resource->weight,
            'variant' => $this->resource->variant ?? [],
            'image' => $this->resource->image,
            'tax_id' => $this->resource->tax_id,
            'tax_rate_percent' => (float) $this->resource->tax_rate_percent,
            'user_id' => $this->resource->user_id,
            'created_at' => $this->resource->created_at,
            'updated_at' => $this->resource->updated_at,
        ];
    }

    // /**
    //  * Get additional data that should be returned with the resource array.
    //  *
    //  * @return array<string, mixed>
    //  */
    // public function with(Request $request): array
    // {
    //     return [
    //         'message' => 'Cart item retrieved successfully',
    //         'status' => 200,
    //         'status_message' => 'OK',
    //         'success' => true,
    //     ];
    // }
}
