<?php

namespace App\Http\Resources\Order;

use App\Http\Resources\OrderItem\OrderItemResource;
use App\Http\Resources\Payment\PaymentResource;
use App\Http\Resources\ShippingAddress\ShippingAddressOrderResource;
use App\Http\Resources\User\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DetailOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $invoiceUrl = null;
        $expiredAt = null;
        if ($this->resource->relationLoaded('latestPayment') && $this->resource->latestPayment) {
            $invoiceUrl = $this->resource->latestPayment->invoice_url;
            $expiredAt = $this->resource->latestPayment->expires_at;
        }

        $shipper = null;
        if (
            !empty($this->resource->expedition) &&
            isset($this->resource->expedition['code'], $this->resource->expedition['service'])
        ) {
            $shipper = $this->resource->expedition['code'] . ' (' . $this->resource->expedition['service'] . ')' ;
        }

        return [
            'id' => $this->resource->id,
            'jubelio_order_id' => $this->resource->jubelio_order_id,
            'jubelio_contact_id' => $this->resource->jubelio_contact_id,
            'user_name' => $this->resource->user_name,
            'user_phone' => $this->resource->user_phone,
            'shipping_full_name' => $this->resource->shipping_full_name,
            'shipping_phone' => $this->resource->shipping_phone,
            'shipping_address' => $this->resource->shipping_address,
            'shipping_province_id' => $this->resource->shipping_province_id,
            'shipping_province' => $this->resource->shipping_province,
            'shipping_city_id' => $this->resource->shipping_city_id,
            'shipping_city' => $this->resource->shipping_city,
            'shipping_district_id' => $this->resource->shipping_district_id,
            'shipping_district' => $this->resource->shipping_district,
            'shipping_subdistrict_id' => $this->resource->shipping_subdistrict_id,
            'shipping_subdistrict' => $this->resource->shipping_subdistrict,
            'shipping_post_code' => $this->resource->shipping_post_code,
            'status' => $this->resource->status,
            'shipping_type' => $this->resource->shipping_type,
            'shipper' => $shipper,
            'tracking_number' => $this->resource->tracking_number,
            'sub_total' => $this->resource->sub_total,
            'total_discount' => $this->resource->total_discount,
            'shipping_cost' => $this->resource->shipping_cost,
            'insurance_cost' => $this->resource->insurance_cost,
            'grand_total' => $this->resource->grand_total,
            'payment_method' => $this->resource->payment_method,
            'invoice_url' => $invoiceUrl,
            'expired_at' => $expiredAt,
            'expedition' => $this->resource->expedition,
            'etc' => $this->resource->etc,
            'created_at' => $this->resource->created_at,
            'updated_at' => $this->resource->updated_at,

            // Relationships
            'user' => new UserResource($this->whenLoaded('user')),
            'shippingAddress' => new ShippingAddressOrderResource($this->whenLoaded('shippingAddress')),
            'items' => OrderItemResource::collection($this->whenLoaded('items')),
            'payments' => PaymentResource::collection($this->whenLoaded('payments')),
        ];
    }
}
