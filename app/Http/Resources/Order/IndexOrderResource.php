<?php

namespace App\Http\Resources\Order;

use App\Http\Resources\Payment\PaymentResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class IndexOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $invoiceUrl = null;
        $expiredAt = null;
        if ($this->resource->relationLoaded('latestPayment') && $this->resource->latestPayment) {
            $invoiceUrl = $this->resource->latestPayment->invoice_url;
            $expiredAt = $this->resource->latestPayment->expires_at;
        }
        $shipper = null;
        if (
            !empty($this->resource->expedition) &&
            isset($this->resource->expedition['code'], $this->resource->expedition['service'])
        ) {
            $shipper = $this->resource->expedition['code'] . ' (' . $this->resource->expedition['service'] . ')';
        }
        return [
            'id' => $this->resource->id,
            'jubelio_order_id' => $this->resource->jubelio_order_id,
            'jubelio_contact_id' => $this->resource->jubelio_contact_id,
            'user_name' => $this->resource->user_name,
            'shipping_full_name' => $this->resource->shipping_full_name,
            'status' => $this->resource->status,
            'shipping_type' => $this->resource->shipping_type,
            'shipper' => $shipper,
            'tracking_number' => $this->resource->tracking_number,
            'sub_total' => $this->resource->sub_total,
            'total_discount' => $this->resource->total_discount,
            'shipping_cost' => $this->resource->shipping_cost,
            'insurance_cost' => $this->resource->insurance_cost,
            'grand_total' => $this->resource->grand_total,
            'invoice_url' => $invoiceUrl,
            'expired_at' => $expiredAt,
            'created_at' => $this->resource->created_at
        ];
    }
}
