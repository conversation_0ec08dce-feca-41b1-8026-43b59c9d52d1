<?php

namespace App\Http\Requests\SenderAddress;

use Illuminate\Foundation\Http\FormRequest;

class IndexSenderAddressRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => 'sometimes|integer|exists:users,id',
            'search' => 'sometimes|string|max:255',
            'is_default' => 'sometimes|boolean',
            'sort_by' => 'sometimes|string|in:id,sender_full_name,sender_phone,sender_province,sender_city,is_default,created_at',
            'sort_direction' => 'sometimes|string|in:asc,desc',
            'per_page' => 'required|integer|min:1|max:100',
            'page' => 'required|integer|min:1',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'search.string' => 'Search term must be a valid string',
            'search.max' => 'Search term cannot exceed 255 characters',
            'user_id.exists' => 'The selected user does not exist',
        ];
    }

    /**
     * Get the validated data with default values.
     */
    public function validated($key = null, $default = null): array
    {
        $validated = parent::validated();

        // Set default values
        $validated['sort_by'] = $validated['sort_by'] ?? 'created_at';
        $validated['sort_direction'] = $validated['sort_direction'] ?? 'desc';

        return $validated;
    }
}
