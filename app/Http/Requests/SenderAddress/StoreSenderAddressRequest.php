<?php

namespace App\Http\Requests\SenderAddress;

use Illuminate\Foundation\Http\FormRequest;

class StoreSenderAddressRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'sender_full_name' => 'required|string|max:255',
            'sender_phone' => 'required|string|max:20',
            'sender_address' => 'required|string|max:1000',
            'sender_province_id' => 'required|string|max:255',
            'sender_province' => 'required|string|max:255',
            'sender_city_id' => 'required|string|max:255',
            'sender_city' => 'required|string|max:255',
            'sender_district_id' => 'required|string|max:255',
            'sender_district' => 'required|string|max:255',
            'sender_subdistrict_id' => 'required|string|max:255',
            'sender_subdistrict' => 'required|string|max:255',
            'sender_post_code' => 'nullable|string|max:10',
            'is_default' => 'sometimes|boolean',
            'user_id' => 'required|integer|exists:users,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'sender_full_name.required' => 'Full name is required',
            'sender_phone.required' => 'Phone number is required',
            'sender_address.required' => 'Address is required',
            'sender_province_id.required' => 'Province ID is required',
            'sender_province.required' => 'Province is required',
            'sender_city_id.required' => 'City ID is required',
            'sender_city.required' => 'City is required',
            'sender_district_id.required' => 'District ID is required',
            'sender_district.required' => 'District is required',
            'sender_subdistrict_id.required' => 'Subdistrict ID is required',
            'sender_subdistrict.required' => 'Subdistrict is required',
            'user_id.required' => 'User ID is required',
            'user_id.exists' => 'Selected user does not exist',
        ];
    }
}
