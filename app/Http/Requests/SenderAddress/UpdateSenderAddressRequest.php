<?php

namespace App\Http\Requests\SenderAddress;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSenderAddressRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'sender_full_name' => 'sometimes|string|max:255',
            'sender_phone' => 'sometimes|string|max:20',
            'sender_address' => 'sometimes|string|max:1000',
            'sender_province_id' => 'sometimes|string|max:255',
            'sender_province' => 'sometimes|string|max:255',
            'sender_city_id' => 'sometimes|string|max:255',
            'sender_city' => 'sometimes|string|max:255',
            'sender_district_id' => 'sometimes|string|max:255',
            'sender_district' => 'sometimes|string|max:255',
            'sender_subdistrict_id' => 'sometimes|string|max:255',
            'sender_subdistrict' => 'sometimes|string|max:255',
            'sender_post_code' => 'sometimes|nullable|string|max:10',
            'is_default' => 'sometimes|boolean',
            'user_id' => 'sometimes|integer|exists:users,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'sender_full_name.string' => 'Full name must be a string',
            'sender_phone.string' => 'Phone number must be a string',
            'sender_address.string' => 'Address must be a string',
            'user_id.integer' => 'User ID must be an integer',
            'user_id.exists' => 'Selected user does not exist',
        ];
    }
}
