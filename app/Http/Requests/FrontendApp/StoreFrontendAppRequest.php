<?php

namespace App\Http\Requests\FrontendApp;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rule;

class StoreFrontendAppRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Assuming only admins can create frontend apps
        return Gate::allows('create', \App\Models\FrontendApp::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'identifier' => 'required|string|max:255|unique:frontend_apps,identifier',
            'auto_assign_to_role' => ['nullable', 'string', Rule::in(User::getRoles())],
        ];
    }
}
