<?php

namespace App\Http\Requests\FrontendApp;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rule;

class UpdateFrontendAppRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Assuming only admins can update frontend apps
        return Gate::allows('update', $this->route('frontend_app'));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $frontendAppId = $this->route('frontend_app')->id;

        return [
            'name' => 'sometimes|required|string|max:255',
            'identifier' => 'sometimes|required|string|max:255|unique:frontend_apps,identifier,' . $frontendAppId,
            'auto_assign_to_role' => ['sometimes', 'nullable', 'string', Rule::in(User::getRoles())],
            'maintenance_status' => 'sometimes|required|boolean',
            'force_logout_before' => 'nullable|date',
        ];
    }
}
