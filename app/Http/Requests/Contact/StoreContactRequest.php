<?php

namespace App\Http\Requests\Contact;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Contact;
use Illuminate\Validation\Rule;

class StoreContactRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'phone' => ['nullable', 'string', 'max:255', Rule::unique('contacts', 'phone')],
            'email' => ['nullable', 'email', 'max:255', Rule::unique('contacts', 'email')],
            'purpose' => ['nullable', 'string', Rule::in(Contact::getPurposes())],
        ];
    }
}
