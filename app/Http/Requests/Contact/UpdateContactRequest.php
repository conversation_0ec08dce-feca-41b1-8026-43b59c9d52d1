<?php

namespace App\Http\Requests\Contact;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Contact;
use Illuminate\Validation\Rule;

class UpdateContactRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|required|string|max:255',
            'phone' => [
                'sometimes',
                'nullable',
                'string',
                'max:255',
                Rule::unique('contacts', 'phone')->ignore($this->contact->id),
            ],
            'email' => [
                'sometimes',
                'nullable',
                'email',
                'max:255',
                Rule::unique('contacts', 'email')->ignore($this->contact->id),
            ],
            'purpose' => ['sometimes', 'nullable', 'string', Rule::in(Contact::getPurposes())],
        ];
    }
}
