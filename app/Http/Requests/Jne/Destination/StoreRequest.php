<?php

namespace App\Http\Requests\Jne\Destination;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'country_name' => 'required|string|max:255',
            'province_name' => 'required|string|max:255',
            'city_name' => 'required|string|max:255',
            'district_name' => 'required|string|max:255',
            'subdistrict_name' => 'required|string|max:255',
            'zip_code' => 'required|string|max:255',
            'tariff_code' => 'required|string|max:255|unique:jne_destinations,tariff_code',
        ];
    }
}
