<?php

namespace App\Http\Requests\Jne\Destination;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'country_name' => 'sometimes|required|string|max:255',
            'province_name' => 'sometimes|required|string|max:255',
            'city_name' => 'sometimes|required|string|max:255',
            'district_name' => 'sometimes|required|string|max:255',
            'subdistrict_name' => 'sometimes|required|string|max:255',
            'zip_code' => 'sometimes|required|string|max:255',
            'tariff_code' => [
                'sometimes',
                'required',
                'string',
                'max:255',
            ],
        ];
    }
}
