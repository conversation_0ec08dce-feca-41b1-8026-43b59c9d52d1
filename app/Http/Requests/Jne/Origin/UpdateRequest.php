<?php

namespace App\Http\Requests\Jne\Origin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $originId = $this->route('jne_origin')->id;

        return [
            'name' => 'sometimes|required|string|max:255',
            'origin_code' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                Rule::unique('jne_origins', 'origin_code')->ignore($originId)
            ],
            'is_default' => 'sometimes|boolean',
        ];
    }
}
