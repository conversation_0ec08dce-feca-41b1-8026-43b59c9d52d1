<?php

namespace App\Http\Requests\Jne\Origin;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'origin_code' => 'required|string|max:255|unique:jne_origins,origin_code',
            'is_default' => 'sometimes|boolean',
        ];
    }
}
