<?php

namespace App\Http\Requests\Jne\Origin;

use Illuminate\Foundation\Http\FormRequest;

class IndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'page' => 'required|integer|min:1',
            'per_page' => 'required|integer|min:1|max:100',
            'search' => 'sometimes|string|max:255',
            'is_default' => 'sometimes|boolean',
            'origin_code' => 'sometimes|string|max:255',
            'name' => 'sometimes|string|max:255',
            'sort_by' => 'sometimes|string|in:created_at,updated_at,name,origin_code,is_default',
            'sort_direction' => 'sometimes|string|in:asc,desc',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'page.required' => 'Page number is required',
            'page.integer' => 'Page must be an integer',
            'page.min' => 'Page must be at least 1',
            'per_page.required' => 'Per page value is required',
            'per_page.integer' => 'Per page must be an integer',
            'per_page.min' => 'Per page must be at least 1',
            'per_page.max' => 'Per page cannot exceed 100',
            'sort_by.in' => 'Invalid sort by value. Allowed values are created_at, updated_at, name, origin_code, is_default',
            'sort_direction.in' => 'Invalid sort direction. Allowed values are asc, desc',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'sort_by' => $this->sort_by ?? 'is_default',
            'sort_direction' => $this->sort_direction ?? 'desc',
        ]);
    }
}
