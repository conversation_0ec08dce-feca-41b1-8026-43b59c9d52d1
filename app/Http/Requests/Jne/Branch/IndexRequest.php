<?php

namespace App\Http\Requests\Jne\Branch;

use Illuminate\Foundation\Http\FormRequest;

class IndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'page' => 'required|integer|min:1',
            'per_page' => 'required|integer|min:1|max:100',
            'search' => 'sometimes|string|max:255',
            'is_default' => 'sometimes|boolean',
            'branch_code' => 'sometimes|string|max:255',
            'name' => 'sometimes|string|max:255',
            'sort_by' => 'sometimes|string|in:created_at,updated_at,name,branch_code,is_default',
            'sort_direction' => 'sometimes|string|in:asc,desc',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'page' => $this->page ?? 1,
            'per_page' => $this->per_page ?? 15,
            'sort_by' => $this->sort_by ?? 'is_default',
            'sort_direction' => $this->sort_direction ?? 'desc',
        ]);
    }
}
