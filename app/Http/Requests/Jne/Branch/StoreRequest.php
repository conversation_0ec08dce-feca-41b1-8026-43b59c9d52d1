<?php

namespace App\Http\Requests\Jne\Branch;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'branch_code' => 'required|string|max:255|unique:jne_branches,branch_code',
            'name' => 'required|string|max:255',
            'is_default' => 'sometimes|boolean',
        ];
    }
}
