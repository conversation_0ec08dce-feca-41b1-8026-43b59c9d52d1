<?php

namespace App\Http\Requests\Jne\Branch;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $branchId = $this->route('jne_branch') ? $this->route('jne_branch')->id : null;

        return [
            'branch_code' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                Rule::unique('jne_branches', 'branch_code')->ignore($branchId)
            ],
            'name' => 'sometimes|required|string|max:255',
            'is_default' => 'sometimes|boolean',
        ];
    }
}
