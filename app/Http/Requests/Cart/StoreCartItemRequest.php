<?php

namespace App\Http\Requests\Cart;

use Illuminate\Foundation\Http\FormRequest;

class StoreCartItemRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'jubelio_item_id' => 'required|integer',
            'jubelio_item_group_id' => 'required|integer',
            'sku' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'unit' => 'required|string|max:50',
            'price' => 'required|numeric|min:0',
            'quantity' => 'required|integer|min:1',
            'weight' => 'required|numeric|min:0',
            'variant' => 'nullable|array',
            'variant.*.label' => 'required|string',
            'variant.*.value' => 'required|string',
            'image' => 'nullable|string',
            'tax_id' => 'required|integer',
            'tax_rate_percent' => 'required|numeric|min:0|max:100',
            'user_id' => 'required|integer',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Ensure the user_id is the currently authenticated user's ID.
        $this->merge([
            'user_id' => $this->user()->id,
        ]);
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'variant.*.label.required_if' => 'Each variant must have a label',
            'variant.*.value.required_if' => 'Each variant must have a value',
        ];
    }
}
