<?php

namespace App\Http\Requests\FixedPrice;

use Illuminate\Foundation\Http\FormRequest;

class BatchUpdateFixedPriceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'prices' => 'required|array',
            'prices.*.jubelio_item_group_id' => 'required|integer',
            'prices.*.jubelio_item_id' => 'required|integer',
            'prices.*.name' => 'required|string|max:255',
            'prices.*.variant' => 'nullable|array',
            'prices.*.original_price' => 'required|numeric|min:0',
            'prices.*.price' => 'required|numeric|min:0|lte:prices.*.original_price',
        ];
    }
}
