<?php

namespace App\Http\Requests\FixedPrice;

use Illuminate\Foundation\Http\FormRequest;

class BatchDestroyFixedPriceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'jubelio_item_ids' => 'required|array',
            'jubelio_item_ids.*' => 'integer|exists:fixed_prices,jubelio_item_id',
        ];
    }
}
