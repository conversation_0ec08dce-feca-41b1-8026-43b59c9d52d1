<?php

namespace App\Http\Requests\ProductDiscount;

use Illuminate\Foundation\Http\FormRequest;

class BatchUpdateProductDiscountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'discounts' => 'required|array',
            'discounts.*.jubelio_item_group_id' => 'required|integer',
            'discounts.*.name' => 'nullable|string',
            'discounts.*.max_discount_percentage' => 'required|numeric|min:0|max:100',
        ];
    }
}
