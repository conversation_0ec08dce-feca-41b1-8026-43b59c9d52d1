<?php

namespace App\Http\Requests\Order;

use App\Models\Order;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreOrderRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'jubelio_order_id' => 'required|integer|unique:orders,jubelio_order_id',
            'shipping_address_id' => [
                'required',
                'integer',
                Rule::exists('shipping_addresses', 'id')->where('user_id', $this->user()->id),
            ],
            'sender_address_id' => [
                'sometimes',
                'nullable',
                'integer',
                Rule::exists('sender_addresses', 'id')->where('user_id', $this->user()->id),
            ],
            'status' => 'sometimes|string|in:' . implode(',', Order::getStatuses()),
            'shipping_type' => ['required', 'string', Rule::in(Order::getShippingTypes())],
            'tracking_number' => [
                'nullable',
                'string',
                'max:255',
                Rule::requiredIf($this->input('shipping_type') === Order::SHIPPING_TYPE_ONLINE_RECIPIENT)
            ],
            'sub_total' => 'required|numeric|min:0',
            'shipping_cost' => 'sometimes|numeric|min:0',
            'insurance_cost' => 'sometimes|numeric|min:0',
            'grand_total' => 'required|numeric|min:0',
            'payment_method' => 'nullable|string|max:100',
            'expedition' => [
                'nullable',
                'array',
                Rule::requiredIf($this->input('shipping_type') === Order::SHIPPING_TYPE_EXPEDITION)
            ],
            'expedition.name' => 'required_with:expedition|string|max:255',
            'expedition.code' => 'required_with:expedition|string|max:50',
            'expedition.service' => 'required_with:expedition|string|max:100',
            'expedition.description' => 'nullable|string|max:255',
            'expedition.cost' => 'required_with:expedition|numeric|min:0',
            'expedition.etd' => 'nullable|string|max:100',
            'expedition.courier_id' => 'sometimes|integer',
            'expedition.courier_service_id' => 'sometimes|integer',
            'etc' => 'nullable|array',

            'items' => 'required|array|min:1',
            'items.*.jubelio_item_id' => 'required|integer',
            'items.*.jubelio_item_group_id' => 'required|integer',
            'items.*.sku' => 'required|string|max:255',
            'items.*.name' => 'required|string|max:255',
            'items.*.unit' => 'required|string|max:255', // example buah, biji, kotak and etc
            'items.*.original_price' => 'required|numeric|min:0',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.weight' => 'required|numeric|min:0',
            'items.*.variant' => 'nullable|array',
            'items.*.image' => 'nullable|string|max:255',
            'items.*.tax_id' => 'required|integer',
            'items.*.tax_rate_percent' => 'required|numeric|min:0|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'jubelio_order_id.required' => 'Order ID is required',
            'jubelio_order_id.unique' => 'Order ID already exists',
            'user_id.required' => 'User ID is required',
            'user_id.exists' => 'Selected user does not exist',
            'sub_total.required' => 'Sub total is required',
            'status.in' => 'Invalid order status',
            'expedition.name.required_with' => 'Expedition name is required when expedition is provided',
            'expedition.code.required_with' => 'Expedition code is required when expedition is provided',
            'expedition.service.required_with' => 'Expedition service is required when expedition is provided',
            'expedition.cost.required_with' => 'Expedition cost is required when expedition is provided',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default status if not provided
        if (!$this->has('status')) {
            $this->merge(['status' => Order::STATUS_PENDING]);
        }

        // Set default values for optional numeric fields
        $defaults = [
            'total_discount' => 0,
            'shipping_cost' => 0,
            'insurance_cost' => 0,
        ];

        foreach ($defaults as $field => $defaultValue) {
            if (!$this->has($field)) {
                $this->merge([$field => $defaultValue]);
            }
        }
    }


}
