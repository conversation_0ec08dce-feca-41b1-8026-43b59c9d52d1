<?php

namespace App\Http\Requests\Order;

use App\Models\Order;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateOrderRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $orderId = $this->route('order')->id;

        return [
            'jubelio_contact_id' => 'sometimes|required|integer',
            'jubelio_order_id' => [
                'sometimes',
                'required',
                'integer',
                Rule::unique('orders', 'jubelio_order_id')->ignore($orderId)
            ],
            'user_id' => 'sometimes|nullable|integer|exists:users,id',
            'shipping_address_id' => 'sometimes|nullable|integer|exists:shipping_addresses,id',
            'user_name' => 'sometimes|required|string|max:255',
            'user_phone' => 'sometimes|required|string|max:20',
            'shipping_full_name' => 'sometimes|required|string|max:255',
            'shipping_phone' => 'sometimes|required|string|max:20',
            'shipping_address' => 'sometimes|required|string|max:65535',
            'shipping_province_id' => 'sometimes|required|string|max:255',
            'shipping_province' => 'sometimes|required|string|max:255',
            'shipping_city_id' => 'sometimes|required|string|max:255',
            'shipping_city' => 'sometimes|required|string|max:255',
            'shipping_district_id' => 'sometimes|required|string|max:255',
            'shipping_district' => 'sometimes|required|string|max:255',
            'shipping_subdistrict_id' => 'sometimes|required|string|max:255',
            'shipping_subdistrict' => 'sometimes|required|string|max:255',
            'shipping_post_code' => 'sometimes|nullable|string|max:10',
            'status' => 'sometimes|required|string|in:' . implode(',', Order::getStatuses()),
            'shipping_type' => 'sometimes|required|string|in:' . implode(',', Order::getShippingTypes()),
            'tracking_number' => 'nullable|string|max:255',
            'sub_total' => 'sometimes|required|numeric|min:0',
            'shipping_cost' => 'sometimes|required|numeric|min:0',
            'insurance_cost' => 'sometimes|required|numeric|min:0',
            'grand_total' => 'sometimes|required|numeric|min:0',
            'payment_method' => 'nullable|string|max:100',
            'expedition' => 'nullable|array',
            'expedition.name' => 'required_with:expedition|string|max:255',
            'expedition.code' => 'required_with:expedition|string|max:50',
            'expedition.service' => 'required_with:expedition|string|max:100',
            'expedition.description' => 'nullable|string|max:255',
            'expedition.cost' => 'required_with:expedition|numeric|min:0',
            'expedition.etd' => 'nullable|string|max:100',
            'etc' => 'nullable|array',

            'items' => 'sometimes|required|array|min:1',
            'items.*.jubelio_item_id' => 'required|integer',
            'items.*.jubelio_item_group_id' => 'required|integer',
            'items.*.sku' => 'required|string|max:255',
            'items.*.name' => 'required|string|max:255',
            'items.*.unit' => 'required|string|max:255', // example buah, biji, kotak and etc
            'items.*.original_price' => 'required|numeric|min:0',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.weight' => 'required|numeric|min:0',
            'items.*.variant' => 'nullable|array',
            'items.*.image' => 'nullable|string|max:255',
            'items.*.tax_id' => 'required|integer',
            'items.*.tax_rate_percent' => 'required|numeric|min:0|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'jubelio_order_id.unique' => 'Order ID already exists',
            'user_id.exists' => 'Selected user does not exist',
            'status.in' => 'Invalid order status',
            'expedition.name.required_with' => 'Expedition name is required when expedition is provided',
            'expedition.code.required_with' => 'Expedition code is required when expedition is provided',
            'expedition.service.required_with' => 'Expedition service is required when expedition is provided',
            'expedition.cost.required_with' => 'Expedition cost is required when expedition is provided',
        ];
    }


}
