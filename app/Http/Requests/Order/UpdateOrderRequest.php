<?php

namespace App\Http\Requests\Order;

use App\Models\Order;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateOrderRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $order = $this->route('order');
        $orderUserId = $order->user_id;

        return [
            'jubelio_contact_id' => 'sometimes|required|integer',
            'jubelio_order_id' => [
                'sometimes',
                'required',
                'integer',
                Rule::unique('orders', 'jubelio_order_id')->ignore($order->id)
            ],
            'user_id' => [
                'sometimes',
                'nullable',
                'integer',
                'exists:users,id',
            ],
            'shipping_address_id' => [
                'sometimes',
                'nullable',
                'integer',
                Rule::exists('shipping_addresses', 'id')->where('user_id', $orderUserId),
            ],
            'sender_address_id' => [
                'sometimes',
                'nullable',
                'integer',
                Rule::exists('sender_addresses', 'id')->where('user_id', $orderUserId),
            ],
            'status' => 'sometimes|required|string|in:' . implode(',', Order::getStatuses()),
            'shipping_type' => 'sometimes|required|string|in:' . implode(',', Order::getShippingTypes()),
            'tracking_number' => 'sometimes|nullable|string|max:255',
            'sub_total' => 'sometimes|required|numeric|min:0',
            'shipping_cost' => 'sometimes|required|numeric|min:0',
            'insurance_cost' => 'sometimes|required|numeric|min:0',
            'grand_total' => 'sometimes|required|numeric|min:0',
            'payment_method' => 'nullable|string|max:100',
            'expedition' => 'sometimes|nullable|array',
            'expedition.name' => 'sometimes|required_with:expedition|string|max:255',
            'expedition.code' => 'sometimes|required_with:expedition|string|max:50',
            'expedition.service' => 'sometimes|required_with:expedition|string|max:100',
            'expedition.description' => 'sometimes|nullable|string|max:255',
            'expedition.cost' => 'sometimes|required_with:expedition|numeric|min:0',
            'expedition.etd' => 'sometimes|nullable|string|max:100',
            'expedition.courier_id' => 'sometimes|integer',
            'expedition.courier_service_id' => 'sometimes|integer',
            'etc' => 'sometimes|nullable|array',

            'items' => 'sometimes|required|array|min:1',
            'items.*.jubelio_item_id' => 'required|integer',
            'items.*.jubelio_item_group_id' => 'required|integer',
            'items.*.sku' => 'required|string|max:255',
            'items.*.name' => 'required|string|max:255',
            'items.*.unit' => 'required|string|max:255', // example buah, biji, kotak and etc
            'items.*.original_price' => 'required|numeric|min:0',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.weight' => 'required|numeric|min:0',
            'items.*.variant' => 'nullable|array',
            'items.*.image' => 'nullable|string|max:255',
            'items.*.tax_id' => 'required|integer',
            'items.*.tax_rate_percent' => 'required|numeric|min:0|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'jubelio_order_id.unique' => 'Order ID already exists',
            'user_id.exists' => 'Selected user does not exist',
            'status.in' => 'Invalid order status',
            'expedition.name.required_with' => 'Expedition name is required when expedition is provided',
            'expedition.code.required_with' => 'Expedition code is required when expedition is provided',
            'expedition.service.required_with' => 'Expedition service is required when expedition is provided',
            'expedition.cost.required_with' => 'Expedition cost is required when expedition is provided',
        ];
    }


}
