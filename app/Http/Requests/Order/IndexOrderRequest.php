<?php

namespace App\Http\Requests\Order;

use App\Models\Order;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class IndexOrderRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
{
    $user = Auth::user();

    $rules = [
        'status' => 'sometimes|string|in:' . implode(',', Order::getStatuses()),
        'jubelio_contact_id' => 'sometimes|integer|min:1',
        'jubelio_order_id' => 'sometimes|integer|min:1',
        'search' => 'sometimes|string|max:255',
        'sort_by' => 'sometimes|string|in:created_at,updated_at,jubelio_order_id,status,grand_total',
        'sort_direction' => 'sometimes|string|in:asc,desc',
        'per_page' => 'required|integer|min:1|max:100',
        'page' => 'required|integer|min:1',
        'date_from' => 'sometimes|date',
        'date_to' => 'sometimes|date|after_or_equal:date_from',
        'min_total' => 'sometimes|numeric|min:0',
        'max_total' => 'sometimes|numeric|min:0|gte:min_total',
    ];

    // Add user_id validation only for admin role
    if ($user && $user->isAdmin()) {
        $rules['user_id'] = 'sometimes|integer|min:1|exists:users,id';
    }

    return $rules;
}

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'status.in' => 'Invalid order status',
            'user_id.exists' => 'Selected user does not exist',
            'sort_by.in' => 'Invalid sort field',
            'sort_direction.in' => 'Sort direction must be asc or desc',
            'date_to.after_or_equal' => 'End date must be after or equal to start date',
            'max_total.gte' => 'Maximum total must be greater than or equal to minimum total',
            'per_page.max' => 'Maximum 100 items per page allowed',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $defaults = [
            'sort_by' => 'created_at',
            'sort_direction' => 'desc',
            // 'per_page' => 15,
            // 'page' => 1,
        ];

        foreach ($defaults as $field => $defaultValue) {
            if (!$this->has($field)) {
                $this->merge([$field => $defaultValue]);
            }
        }
    }
}
