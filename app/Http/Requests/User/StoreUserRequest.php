<?php

namespace App\Http\Requests\User;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class StoreUserRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'string', Password::defaults()],
            'phone' => [
                'required',
                'string',
                'max:20',
                Rule::unique('users')->where(function ($query) {
                    if ($this->input('role') === User::ROLE_RESELLER) {
                        return $query->where('role', User::ROLE_RESELLER);
                    }
                    // For other roles, the uniqueness rule for phone numbers is not applied.
                    // We add a condition that is always false to bypass the check.
                    return $query->whereRaw('1 = 0');
                }),
            ],
            'role' => ['required', 'string', Rule::in(User::getRoles())],
            'is_active' => 'sometimes|boolean',
            'membership_level_id' => 'nullable|integer|exists:membership_levels,id',
            // 'jubelio_contact_id' => 'nullable|integer|min:1', // not needed on create all type user now, but lets keep it now
            // 'frontend_app_ids' => 'required|array',
            // 'frontend_app_ids.*' => 'required|integer|exists:frontend_apps,id',
        ];
    }
}
