<?php

namespace App\Http\Requests\User;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class UpdateUserRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $userId = $this->route('user') ? $this->route('user')->id : null;

        return [
            'name' => 'sometimes|required|string|max:255',
            'email' => [
                'sometimes',
                'required',
                'email',
                Rule::unique('users', 'email')->ignore($userId)
            ],
            'phone' => [
                'sometimes',
                'nullable',
                'string',
                'max:20',
                Rule::unique('users')->ignore($userId)->where(function ($query) {
                    // only validate uniqueness if the user is a reseller
                    $role = $this->input('role', $this->route('user')->role);
                    if ($role === User::ROLE_RESELLER) {
                        return $query->where('role', User::ROLE_RESELLER);
                    }
                    // For other roles, the uniqueness rule for phone numbers is not applied.
                    // We add a condition that is always false to bypass the check.
                    return $query->whereRaw('1 = 0');
                }),
            ],
            'password' => ['sometimes', 'required', 'string', Password::defaults()],
            'role' => ['sometimes', 'required', 'string', Rule::in(User::getRoles())],
            'is_active' => 'sometimes|boolean',
            'membership_level_id' => 'nullable|integer|exists:membership_levels,id',
            // 'jubelio_contact_id' => 'sometimes|nullable|integer', // not needed on edit all type user now, but lets keep it now
            // 'frontend_app_ids' => 'sometimes|required|array',
            // 'frontend_app_ids.*' => 'required|integer|exists:frontend_apps,id',
        ];
    }
}
