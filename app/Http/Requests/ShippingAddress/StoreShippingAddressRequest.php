<?php

namespace App\Http\Requests\ShippingAddress;

use Illuminate\Foundation\Http\FormRequest;

class StoreShippingAddressRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'shipping_full_name' => 'required|string|max:255',
            'shipping_phone' => 'required|string|max:20',
            'shipping_address' => 'required|string|max:1000',
            'shipping_province_id' => 'required|string|max:255',
            'shipping_province' => 'required|string|max:255',
            'shipping_city_id' => 'required|string|max:255',
            'shipping_city' => 'required|string|max:255',
            'shipping_district_id' => 'required|string|max:255',
            'shipping_district' => 'required|string|max:255',
            'shipping_subdistrict_id' => 'required|string|max:255',
            'shipping_subdistrict' => 'required|string|max:255',
            'shipping_post_code' => 'nullable|string|max:10',
            'user_id' => 'required|integer|exists:users,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'shipping_full_name.required' => 'Full name is required',
            'shipping_phone.required' => 'Phone number is required',
            'shipping_address.required' => 'Address is required',
            'shipping_province_id.required' => 'Province ID is required',
            'shipping_province.required' => 'Province is required',
            'shipping_city_id.required' => 'City ID is required',
            'shipping_city.required' => 'City is required',
            'shipping_district_id.required' => 'District ID is required',
            'shipping_district.required' => 'District is required',
            'shipping_subdistrict_id.required' => 'Subdistrict ID is required',
            'shipping_subdistrict.required' => 'Subdistrict is required',
            'user_id.required' => 'User ID is required',
            'user_id.exists' => 'Selected user does not exist',
        ];
    }
}
