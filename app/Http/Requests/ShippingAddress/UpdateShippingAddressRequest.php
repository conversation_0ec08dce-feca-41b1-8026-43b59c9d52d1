<?php

namespace App\Http\Requests\ShippingAddress;

use Illuminate\Foundation\Http\FormRequest;

class UpdateShippingAddressRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'shipping_full_name' => 'sometimes|string|max:255',
            'shipping_phone' => 'sometimes|string|max:20',
            'shipping_address' => 'sometimes|string|max:1000',
            'shipping_province_id' => 'sometimes|string|max:255',
            'shipping_province' => 'sometimes|string|max:255',
            'shipping_city_id' => 'sometimes|string|max:255',
            'shipping_city' => 'sometimes|string|max:255',
            'shipping_district_id' => 'sometimes|string|max:255',
            'shipping_district' => 'sometimes|string|max:255',
            'shipping_subdistrict_id' => 'sometimes|string|max:255',
            'shipping_subdistrict' => 'sometimes|string|max:255',
            'shipping_post_code' => 'sometimes|nullable|string|max:10',
            'user_id' => 'sometimes|integer|exists:users,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'shipping_full_name.string' => 'Full name must be a string',
            'shipping_phone.string' => 'Phone number must be a string',
            'shipping_address.string' => 'Address must be a string',
            'user_id.integer' => 'User ID must be an integer',
            'user_id.exists' => 'Selected user does not exist',
        ];
    }
}
