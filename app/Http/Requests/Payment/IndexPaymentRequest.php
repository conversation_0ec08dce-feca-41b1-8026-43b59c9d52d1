<?php

namespace App\Http\Requests\Payment;

use App\Models\Payment;
use Illuminate\Foundation\Http\FormRequest;

class IndexPaymentRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => 'sometimes|string|in:pending,paid,expired,failed',
            'order_id' => 'sometimes|integer|min:1|exists:orders,id',
            'search' => 'sometimes|string|max:255',
            'sort_by' => 'sometimes|string|in:created_at,updated_at,status,amount,paid_at',
            'sort_direction' => 'sometimes|string|in:asc,desc',
            'per_page' => 'required|integer|min:1|max:100',
            'page' => 'required|integer|min:1',
            'date_from' => 'sometimes|date',
            'date_to' => 'sometimes|date|after_or_equal:date_from',
            'min_amount' => 'sometimes|numeric|min:0',
            'max_amount' => 'sometimes|numeric|min:0|gte:min_amount',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'status.in' => 'Invalid payment status',
            'order_id.exists' => 'Selected order does not exist',
            'sort_by.in' => 'Invalid sort field',
            'sort_direction.in' => 'Sort direction must be asc or desc',
            'date_to.after_or_equal' => 'End date must be after or equal to start date',
            'max_amount.gte' => 'Maximum amount must be greater than or equal to minimum amount',
            'per_page.max' => 'Maximum 100 items per page allowed',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $defaults = [
            'sort_by' => 'created_at',
            'sort_direction' => 'desc',
        ];

        foreach ($defaults as $field => $defaultValue) {
            if (!$this->has($field)) {
                $this->merge([$field => $defaultValue]);
            }
        }
    }
}
