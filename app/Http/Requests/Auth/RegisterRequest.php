<?php

namespace App\Http\Requests\Auth;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\Rule;

class RegisterRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Password::defaults()],
            'phone' => [
                'required',
                'string',
                'min:10',
                'max:20',
                // This rule assumes that any user registering through this form
                // is a 'reseller'. Therefore, it checks for phone number
                // uniqueness against all existing resellers.
                Rule::unique('users')->where(function ($query) {
                    return $query->where('role', User::ROLE_RESELLER);
                }),
            ],
        ];
    }
}
