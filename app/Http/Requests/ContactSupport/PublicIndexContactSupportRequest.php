<?php

namespace App\Http\Requests\ContactSupport;

use App\Models\ContactSupport;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PublicIndexContactSupportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Validate that 'category' is a string and is one of the valid categories.
            'category' => ['sometimes', 'string', Rule::in(ContactSupport::getCategories())],
            // Validate that 'type' is a string and is one of the valid types defined in the model.
            'type' => ['sometimes', 'string', Rule::in(ContactSupport::getTypes())],
            // Validate that 'limit' is an integer, with a minimum of 1.
            'limit' => ['sometimes', 'integer', 'min:1'],
        ];
    }
}
