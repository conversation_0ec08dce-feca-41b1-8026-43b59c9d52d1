<?php

namespace App\Http\Requests\JubelioShipment;

use Illuminate\Foundation\Http\FormRequest;

class GetRatesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'origin' => 'required|array',
            'origin.area_id' => 'required|string',
            'origin.zipcode' => 'required|string',
            'origin.coordinate' => 'nullable|array',
            'destination' => 'required|array',
            'destination.area_id' => 'required|string',
            'destination.zipcode' => 'required|string',
            'service_category_id' => 'required|string',
            'weight' => 'required|integer', // total weight of items
            'total_value' => 'required|integer', // total value of items
            'items' => 'required|array',
            'items.*.item_name' => 'required|string',
            'items.*.value' => 'required|integer',
            'items.*.quantity' => 'required|integer',
            'items.*.length' => 'required|integer',
            'items.*.width' => 'required|integer',
            'items.*.height' => 'required|integer',
            'items.*.weight' => 'required|integer',
            'items.*.category' => 'required|array',
            'items.*.category.label' => 'required|string',
            'items.*.category.value' => 'required|string',
        ];
    }
}
