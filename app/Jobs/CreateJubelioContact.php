<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\Jubelio;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CreateJubelioContact implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The user instance.
     *
     * @var \App\Models\User
     */
    protected $user;

    /**
     * Create a new job instance.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @param  \App\Services\Jubelio  $jubelioService
     * @return void
     */
    public function handle(Jubelio $jubelioService): void
    {
        // Refresh the model to get the latest data from the database
        $this->user->refresh();

        if (is_null($this->user->jubelio_contact_id)) {
            Log::info("Job running: Creating Jubelio contact for user {$this->user->id}");
            $jubelioData = $jubelioService->createUser($this->user);

            if ($jubelioData && isset($jubelioData['contact_id'])) {
                $this->user->update(['jubelio_contact_id' => $jubelioData['contact_id']]);
                Log::info("Job success: Jubelio contact created for user {$this->user->id}");
            } else {
                Log::warning("Job failed to get contact_id from Jubelio for user {$this->user->id}");
                // We can throw an exception here to make the job retry
                $this->fail(new \Exception("Failed to get contact_id from Jubelio for user {$this->user->id}"));
            }
        } else {
            Log::info("Job skipped: User {$this->user->id} already has a Jubelio contact ID.");
        }
    }
}
