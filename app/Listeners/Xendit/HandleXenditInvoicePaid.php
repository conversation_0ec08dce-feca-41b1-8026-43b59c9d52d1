<?php

namespace App\Listeners\Xendit;

use App\Events\Xendit\XenditInvoicePaid;
use App\Models\CartItem;
use App\Models\JneBranch;
use App\Models\JneDestination;
use App\Models\JneOrigin;
use App\Models\Order;
use App\Models\Payment;
use App\Models\User;
use App\Notifications\OrderPaidNotification;
use App\Services\AirwaybillService;
use App\Services\Jne;
use App\Services\JneAirwaybillService;
use App\Services\Jubelio;
use App\Services\NotificationService;
use App\Services\JubelioShipmentAirwaybillService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class HandleXenditInvoicePaid implements ShouldQueue
{
    use InteractsWithQueue;

    protected Jubelio $jubelio;
    protected Jne $jne;
    protected NotificationService $notificationService;
    protected AirwaybillService $airwaybillService;
    protected JneAirwaybillService $jneAirwaybillService;
    protected JubelioShipmentAirwaybillService $jubelioShipmentAirwaybillService;

    /**
     * Create the event listener.
     */
    public function __construct(
        Jubelio $jubelio,
        Jne $jne,
        NotificationService $notificationService,
        AirwaybillService $airwaybillService,
        JneAirwaybillService $jneAirwaybillService,
        JubelioShipmentAirwaybillService $jubelioShipmentAirwaybillService
    ) {
        $this->jubelio = $jubelio;
        $this->jne = $jne;
        $this->notificationService = $notificationService;
        $this->airwaybillService = $airwaybillService;
        $this->jneAirwaybillService = $jneAirwaybillService;
        $this->jubelioShipmentAirwaybillService = $jubelioShipmentAirwaybillService;
    }

    /**
     * Handle the event.
     */
    public function handle(XenditInvoicePaid $event): void
    {
        $data = $event->data;
        Log::info('Xendit invoice paid webhook received:', $data);
        Log::info('Payment status: ' . $data['status']);
        Log::info('Payment external_id: ' . $data['external_id']);

        // Check if the status is 'PAID'
        if (isset($data['status']) && $data['status'] === 'PAID') {
            $externalId = $data['external_id'];
            Log::info('Processing payment for external_id: ' . $externalId);

            // Use a database transaction to ensure data integrity
            DB::transaction(function () use ($data, $externalId) {
                $payment = Payment::with(['order.user', 'order.items'])
                    ->where('external_id', $externalId)
                    ->first();

                if (!$payment || $payment->status !== 'pending') {
                    Log::warning('Payment not found or already processed for external_id:', ['external_id' => $externalId]);
                    return;
                }

                $this->updatePayment($payment, $data);

                /** @var Order $order */
                $order = $payment->order;
                $oldStatus = $order->status;
                $newStatus = Order::STATUS_PAID;

                $this->updateOrderStatus($order, $newStatus);
                $this->deleteCartItems($order);
                $this->sendNotifications($order, $oldStatus, $newStatus);
                $this->updateJubelioStatus($order);

                // on development
                // if ($order->shipping_type === Order::SHIPPING_TYPE_EXPEDITION) {
                //     $this->airwaybillService->handleExpedition($order);
                // }

                if ($order->shipping_type === Order::SHIPPING_TYPE_JNE_EXPEDITION) {
                    $result = $this->jneAirwaybillService->handleJneExpedition($order);
                    if (!$result['success']) {
                        Log::error('Failed to generate JNE airwaybill for order #' . $order->id . ' in listener: ' . $result['message']);
                    }
                }

                if ($order->shipping_type === Order::SHIPPING_TYPE_JUBELIO_SHIPMENT) {
                    $result = $this->jubelioShipmentAirwaybillService->handle($order);
                    if (!$result['success']) {
                        Log::error('Failed to generate Jubelio Shipment airwaybill for order #' . $order->id . ' in listener: ' . $result['message']);
                    }
                }
            });
        }
    }

    private function updatePayment(Payment $payment, array $data): void
    {
        $updateData = [
            'status' => 'paid',
            'paid_at' => $data['paid_at'],
            'paid_amount' => $data['paid_amount'],
            'webhook_payload' => $data,
        ];

        if (isset($data['payment_method'])) {
            $updateData['payment_channel'] = $data['payment_channel'] ?? null;
            if ($data['payment_method'] === 'BANK_TRANSFER') {
                $updateData['payment_destination'] = $data['payment_destination'] ?? null;
            }
        }

        $payment->update($updateData);
        Log::info('Payment updated: ' . json_encode($payment));
    }

    private function updateOrderStatus(Order $order, string $newStatus): void
    {
        $order->update(['status' => $newStatus]);
        Log::info("Order #{$order->id} status updated to {$newStatus}");
    }

    private function deleteCartItems(Order $order): void
    {
        if (!$order->user_id) {
            return;
        }

        $order->load('items');

        foreach ($order->items as $orderItem) {
            $cartItem = CartItem::where('user_id', $order->user_id)
                ->where('jubelio_item_id', $orderItem->jubelio_item_id)
                ->first();

            if ($cartItem) {
                if ($cartItem->quantity <= $orderItem->quantity) {
                    // If cart has same or fewer items than ordered, delete it
                    $cartItem->delete();
                    Log::info("Deleted cart item for user {$order->user_id}, Jubelio item ID: {$orderItem->jubelio_item_id}");
                } else {
                    // If cart has more items than ordered, just decrease the quantity
                    $cartItem->decrement('quantity', $orderItem->quantity);
                    Log::info("Updated cart item quantity for user {$order->user_id}, Jubelio item ID: {$orderItem->jubelio_item_id}. New quantity: {$cartItem->quantity}");
                }
            }
        }
    }

    private function sendNotifications(Order $order, string $oldStatus, string $newStatus): void
    {
        try {
            // Notify the user who placed the order using their preferences
            if ($order->user) {
                Log::info("Sending 'order_paid' notification to user {$order->user->id}");
                $this->notificationService->send(
                    $order->user,
                    new OrderPaidNotification($order),
                    'order_paid'
                );
            }

            // Notify all admins with an in-app notification only
            $admins = User::where('role', 'admin')->get();
            foreach ($admins as $admin) {
                // Don't send another notification to an admin who is also the order owner
                if ($order->user && $admin->id === $order->user->id) {
                    continue;
                }
                Log::info("Sending 'order_paid' in-app notification to admin {$admin->id}");
                $this->notificationService->sendInAppOnly(
                    $admin,
                    new OrderPaidNotification($order)
                );
            }
        } catch (\Exception $e) {
            Log::error('Failed to send order paid notification for order #' . $order->id . ': ' . $e->getMessage());
        }
    }

    private function updateJubelioStatus(Order $order): void
    {
        if (!$order->jubelio_order_id) {
            return;
        }

        Log::info('Updating Jubelio order status to paid. Order ID: ' . $order->jubelio_order_id);
        $this->jubelio->setOrderAsPaid($order->jubelio_order_id);

        // $isReady = false;
        // $maxRetries = 5;
        // $retryCount = 0;

        // while (!$isReady && $retryCount < $maxRetries) {
        //     $isReady = $this->jubelio->isOrderReadyToProcess($order->jubelio_order_id);
        //     if (!$isReady) {
        //         $retryCount++;
        //         Log::info("Order {$order->jubelio_order_id} not ready. Retrying ({$retryCount}/{$maxRetries})...");
        //         sleep(5);
        //     }
        // }

        // if ($isReady) {
        //     Log::info("Order {$order->jubelio_order_id} is ready. Setting as ready to pick.");
        //     $this->jubelio->setReadyToPick([$order->jubelio_order_id]);
        // } else {
        //     Log::error("Order {$order->jubelio_order_id} was not ready to be picked after {$maxRetries} retries.");
        // }
    }

}
