<?php

namespace App\Listeners\Xendit;

use App\Events\Xendit\XenditInvoicePaid;
use App\Models\CartItem;
use App\Models\JneBranch;
use App\Models\JneDestination;
use App\Models\JneOrigin;
use App\Models\Order;
use App\Models\Payment;
use App\Models\User;
use App\Notifications\OrderPaidNotification;
use App\Services\Jne;
use App\Services\Jubelio;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class HandleXenditInvoicePaid implements ShouldQueue
{
    use InteractsWithQueue;

    protected Jubelio $jubelio;
    protected Jne $jne;
    protected NotificationService $notificationService;

    /**
     * Create the event listener.
     */
    public function __construct(Jubelio $jubelio, Jne $jne, NotificationService $notificationService)
    {
        $this->jubelio = $jubelio;
        $this->jne = $jne;
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the event.
     */
    public function handle(XenditInvoicePaid $event): void
    {
        $data = $event->data;
        Log::info('Xendit invoice paid webhook received:', $data);
        Log::info('Payment status: ' . $data['status']);
        Log::info('Payment external_id: ' . $data['external_id']);

        // Check if the status is 'PAID'
        if (isset($data['status']) && $data['status'] === 'PAID') {
            $externalId = $data['external_id'];
            Log::info('Processing payment for external_id: ' . $externalId);

            // Use a database transaction to ensure data integrity
            DB::transaction(function () use ($data, $externalId) {
                $payment = Payment::with(['order.user', 'order.items'])
                    ->where('external_id', $externalId)
                    ->first();

                if (!$payment || $payment->status !== 'pending') {
                    Log::warning('Payment not found or already processed for external_id:', ['external_id' => $externalId]);
                    return;
                }

                $this->updatePayment($payment, $data);

                /** @var Order $order */
                $order = $payment->order;
                $oldStatus = $order->status;
                $newStatus = Order::STATUS_PAID;

                $this->updateOrderStatus($order, $newStatus);
                $this->deleteCartItems($order);
                $this->sendNotifications($order, $oldStatus, $newStatus);
                $this->updateJubelioStatus($order);

                if ($order->shipping_type === Order::SHIPPING_TYPE_JNE_EXPEDITION) {
                    $jnePayload = $this->buildJnePayload($order);
                    $jneResponse = $this->jne->generateAirwaybill($jnePayload);

                    if ($jneResponse && isset($jneResponse[0]['detail'][0]['cnote_no'])) {
                        $trackingNumber = $jneResponse[0]['detail'][0]['cnote_no'];

                        $currentSalesOrder = $this->jubelio->getSalesOrderDetail($order->jubelio_order_id);
                        if ($currentSalesOrder) {
                            $updatePayload = $this->buildJubelioUpdatePayload($currentSalesOrder, $trackingNumber);
                            $this->jubelio->createOrUpdateSalesOrder($updatePayload);

                            $order->update(['tracking_number' => $trackingNumber]);
                        }
                    }
                }
            });
        }
    }

    private function updatePayment(Payment $payment, array $data): void
    {
        $updateData = [
            'status' => 'paid',
            'paid_at' => $data['paid_at'],
            'paid_amount' => $data['paid_amount'],
            'webhook_payload' => $data,
        ];

        if (isset($data['payment_method'])) {
            $updateData['payment_channel'] = $data['payment_channel'] ?? null;
            if ($data['payment_method'] === 'BANK_TRANSFER') {
                $updateData['payment_destination'] = $data['payment_destination'] ?? null;
            }
        }

        $payment->update($updateData);
        Log::info('Payment updated: ' . json_encode($payment));
    }

    private function updateOrderStatus(Order $order, string $newStatus): void
    {
        $order->update(['status' => $newStatus]);
        Log::info("Order #{$order->id} status updated to {$newStatus}");
    }

    private function deleteCartItems(Order $order): void
    {
        if (!$order->user_id) {
            return;
        }

        $order->load('items');

        foreach ($order->items as $orderItem) {
            $cartItem = CartItem::where('user_id', $order->user_id)
                ->where('jubelio_item_id', $orderItem->jubelio_item_id)
                ->first();

            if ($cartItem) {
                if ($cartItem->quantity <= $orderItem->quantity) {
                    // If cart has same or fewer items than ordered, delete it
                    $cartItem->delete();
                    Log::info("Deleted cart item for user {$order->user_id}, Jubelio item ID: {$orderItem->jubelio_item_id}");
                } else {
                    // If cart has more items than ordered, just decrease the quantity
                    $cartItem->decrement('quantity', $orderItem->quantity);
                    Log::info("Updated cart item quantity for user {$order->user_id}, Jubelio item ID: {$orderItem->jubelio_item_id}. New quantity: {$cartItem->quantity}");
                }
            }
        }
    }

    private function sendNotifications(Order $order, string $oldStatus, string $newStatus): void
    {
        try {
            // Notify the user who placed the order using their preferences
            if ($order->user) {
                Log::info("Sending 'order_paid' notification to user {$order->user->id}");
                $this->notificationService->send(
                    $order->user,
                    new OrderPaidNotification($order),
                    'order_paid'
                );
            }

            // Notify all admins with an in-app notification only
            $admins = User::where('role', 'admin')->get();
            foreach ($admins as $admin) {
                // Don't send another notification to an admin who is also the order owner
                if ($order->user && $admin->id === $order->user->id) {
                    continue;
                }
                Log::info("Sending 'order_paid' in-app notification to admin {$admin->id}");
                $this->notificationService->sendInAppOnly(
                    $admin,
                    new OrderPaidNotification($order)
                );
            }
        } catch (\Exception $e) {
            Log::error('Failed to send order paid notification for order #' . $order->id . ': ' . $e->getMessage());
        }
    }

    private function updateJubelioStatus(Order $order): void
    {
        if (!$order->jubelio_order_id) {
            return;
        }

        Log::info('Updating Jubelio order status to paid. Order ID: ' . $order->jubelio_order_id);
        $this->jubelio->setOrderAsPaid($order->jubelio_order_id);

        // $isReady = false;
        // $maxRetries = 5;
        // $retryCount = 0;

        // while (!$isReady && $retryCount < $maxRetries) {
        //     $isReady = $this->jubelio->isOrderReadyToProcess($order->jubelio_order_id);
        //     if (!$isReady) {
        //         $retryCount++;
        //         Log::info("Order {$order->jubelio_order_id} not ready. Retrying ({$retryCount}/{$maxRetries})...");
        //         sleep(5);
        //     }
        // }

        // if ($isReady) {
        //     Log::info("Order {$order->jubelio_order_id} is ready. Setting as ready to pick.");
        //     $this->jubelio->setReadyToPick([$order->jubelio_order_id]);
        // } else {
        //     Log::error("Order {$order->jubelio_order_id} was not ready to be picked after {$maxRetries} retries.");
        // }
    }

    private function buildJnePayload(Order $order): array
    {
        $order->load('items');
        $goodsDescription = $order->items->pluck('name')->implode(', ');
        $totalQuantity = $order->items->sum('quantity');
        $totalWeight = $order->items->reduce(function ($total, $item) {
            return $total + ($item->weight ?: 0) * $item->quantity;
        }, 0);
        $totalWeightInKg = ceil($totalWeight / 1000);
        $defaultOrigin = JneOrigin::where('is_default', true)->first();
        $defaultBranch = JneBranch::where('is_default', true)->first();

        $destination = $this->findJneDestination($order);

        return [
            'OLSHOP_BRANCH' => $defaultBranch ? $defaultBranch->branch_code : config('jne.branch_code'),
            'OLSHOP_CUST' => $order->user_id,
            'OLSHOP_ORDERID' => $order->id,
            'OLSHOP_SHIPPER_NAME' => config('jne.shipper_name'),
            'OLSHOP_SHIPPER_ADDR1' => config('jne.shipper_address'),
            'OLSHOP_SHIPPER_ADDR2' => config('jne.shipper_address'),
            'OLSHOP_SHIPPER_CITY' => config('jne.shipper_city'),
            'OLSHOP_SHIPPER_ZIP' => config('jne.shipper_zip'),
            'OLSHOP_SHIPPER_PHONE' => config('jne.shipper_phone'),
            'OLSHOP_RECEIVER_NAME' => $order->shipping_full_name,
            'OLSHOP_RECEIVER_ADDR1' => $order->shipping_address,
            'OLSHOP_RECEIVER_ADDR2' => $order->shipping_subdistrict,
            'OLSHOP_RECEIVER_ADDR3' => $order->shipping_district,
            'OLSHOP_RECEIVER_CITY' => $order->shipping_city,
            'OLSHOP_RECEIVER_REGION' => $order->shipping_province,
            'OLSHOP_RECEIVER_ZIP' => $order->shipping_post_code,
            'OLSHOP_RECEIVER_PHONE' => $order->shipping_phone,
            'OLSHOP_QTY' => $totalQuantity,
            'OLSHOP_WEIGHT' => $totalWeightInKg,
            'OLSHOP_GOODSDESC' => $goodsDescription,
            'OLSHOP_GOODSVALUE' => $order->sub_total,
            'OLSHOP_INST' => 'Take care the products',
            'OLSHOP_ORIG' => $defaultOrigin ? $defaultOrigin->origin_code : config('jne.origin_code'),
            'OLSHOP_DEST' => $destination ? $destination->tariff_code : '',
            'OLSHOP_SERVICE' => $order->expedition['service'],
        ];
    }

    private function buildJubelioUpdatePayload(array $currentSalesOrder, string $trackingNumber): array
    {
        $payload = [
            'salesorder_id' => $currentSalesOrder['salesorder_id'],
            'salesorder_no' => $currentSalesOrder['salesorder_no'],
            'contact_id' => $currentSalesOrder['contact_id'],
            'customer_name' => $currentSalesOrder['customer_name'],
            'transaction_date' => $currentSalesOrder['transaction_date'],
            'location_id' => $currentSalesOrder['location_id'],
            'source' => $currentSalesOrder['source'],
            'items' => [],
            'is_paid' => true,
        ];

        foreach ($currentSalesOrder['items'] as $item) {
            $payload['items'][] = [
                'salesorder_detail_id' => $item['salesorder_detail_id'],
                'item_id' => $item['item_id'],
                'tax_id' => $item['tax_id'],
                'price' => (float) $item['price'],
                'unit' => $item['unit'],
                'qty_in_base' => (float) $item['qty_in_base'],
                'disc' => (float) $item['disc'],
                'disc_amount' => (float) $item['disc_amount'],
                'tax_amount' => (float) $item['tax_amount'],
                'amount' => (float) $item['amount'],
                'location_id' => $item['loc_id'],
                'serial_no' => $item['serial_no'],
                'description' => $item['description'],
                'shipper' => $currentSalesOrder['shipper'],
                'channel_order_detail_id' => $item['channel_order_detail_id'],
                'tracking_no' => $trackingNumber,
            ];
        }

        return $payload;
    }
    private function normalizeCity(string $city = ''): string
    {
        return trim(preg_replace('/^(KAB\.|KOTA\s)/i', '', $city));
    }

    private function findJneDestination(Order $order): ?JneDestination
    {
        $normalizedCity = $this->normalizeCity($order->shipping_city);

        $queries = [
            [
                'province_name' => $order->shipping_province,
                'city_name' => $normalizedCity,
                'district_name' => $order->shipping_district,
                'subdistrict_name' => $order->shipping_subdistrict,
                'zip_code' => $order->shipping_post_code,
            ],
            [
                'province_name' => $order->shipping_province,
                'city_name' => $normalizedCity,
                'district_name' => $order->shipping_district,
                'subdistrict_name' => $order->shipping_subdistrict,
            ],
            [
                'province_name' => $order->shipping_province,
                'city_name' => $normalizedCity,
                'district_name' => $order->shipping_district,
            ],
            [
                'province_name' => $order->shipping_province,
                'city_name' => $normalizedCity,
            ],
            [
                'province_name' => $order->shipping_province,
            ],
        ];

        foreach ($queries as $query) {
            $destination = JneDestination::where($query)->first();
            if ($destination) {
                return $destination;
            }
        }

        return null;
    }
}
