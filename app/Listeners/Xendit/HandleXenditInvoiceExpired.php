<?php

namespace App\Listeners\Xendit;

use App\Events\Xendit\XenditInvoiceExpired;
use App\Models\Order;
use App\Models\Payment;
use App\Models\User;
use App\Notifications\OrderCancelledNotification;
use App\Services\Jubelio;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HandleXenditInvoiceExpired implements ShouldQueue
{
    use InteractsWithQueue;

    protected Jubelio $jubelio;
    protected NotificationService $notificationService;

    /**
     * Create the event listener.
     */
    public function __construct(Jubelio $jubelio, NotificationService $notificationService)
    {
        $this->jubelio = $jubelio;
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the event.
     */
    public function handle(XenditInvoiceExpired $event): void
    {
        $data = $event->data;
        Log::info('Xendit invoice expired webhook received:', $data);

        if (isset($data['status']) && $data['status'] === 'EXPIRED') {
            $externalId = $data['external_id'];
            Log::info('Processing expired invoice for external_id: ' . $externalId);

            DB::transaction(function () use ($data, $externalId) {
                $payment = Payment::where('external_id', $externalId)->first();

                if ($payment && $payment->status === 'pending') {
                    $this->processExpiredPayment($payment, $data);
                } else {
                    Log::warning('Payment not found or not in pending state for external_id:', ['external_id' => $externalId]);
                }
            });
        }
    }

    /**
     * Process the logic for an expired payment.
     *
     * @param Payment $payment
     * @param array $data
     * @return void
     */
    private function processExpiredPayment(Payment $payment, array $data): void
    {
        $this->updatePaymentToExpired($payment, $data);

        /** @var Order $order */
        $order = $payment->order;
        $oldStatus = $order->status;
        $newStatus = Order::STATUS_CANCELLED;

        $this->updateOrderStatusToCancelled($order, $newStatus);
        $this->notifyUsersAboutCancellation($order, $oldStatus, $newStatus);
        $this->cancelJubelioSalesOrder($order);
    }

    /**
     * Update the payment status to 'expired'.
     *
     * @param Payment $payment
     * @param array $data
     * @return void
     */
    private function updatePaymentToExpired(Payment $payment, array $data): void
    {
        $payment->update([
            'status' => 'expired',
            'webhook_payload' => $data,
        ]);
        Log::info('Payment updated to expired: ' . json_encode($payment));
    }

    /**
     * Update the order status to 'cancelled'.
     *
     * @param Order $order
     * @param string $newStatus
     * @return void
     */
    private function updateOrderStatusToCancelled(Order $order, string $newStatus): void
    {
        $order->update(['status' => $newStatus]);
        Log::info('Order updated to cancelled: ' . json_encode($order));
    }

    /**
     * Notify relevant users about the order cancellation.
     *
     * @param Order $order
     * @param string $oldStatus
     * @param string $newStatus
     * @return void
     */
    private function notifyUsersAboutCancellation(Order $order, string $oldStatus, string $newStatus): void
    {
        try {
            // Notify the user who placed the order using their preferences
            if ($order->user) {
                Log::info("Sending 'order_cancelled' notification to user {$order->user->id}");
                $this->notificationService->send(
                    $order->user,
                    new OrderCancelledNotification($order, 'Invoice expired.'),
                    'order_cancelled'
                );
            }

            // Notify all admins with an in-app notification only
            $admins = User::where('role', 'admin')->get();
            foreach ($admins as $admin) {
                // Don't send another notification to an admin who is also the order owner
                if ($order->user && $admin->id === $order->user->id) {
                    continue;
                }
                Log::info("Sending 'order_cancelled' in-app notification to admin {$admin->id}");
                $this->notificationService->sendInAppOnly(
                    $admin,
                    new OrderCancelledNotification($order, 'Invoice expired.')
                );
            }
        } catch (\Exception $e) {
            Log::error('Failed to send order cancelled notification for order #' . $order->id . ': ' . $e->getMessage());
        }
    }

    /**
     * Cancel the sales order in Jubelio.
     *
     * @param Order $order
     * @return void
     */
    private function cancelJubelioSalesOrder(Order $order): void
    {
        if (!$order->jubelio_order_id) {
            return;
        }

        Log::info('Updating Jubelio sales order to cancelled. Jubelio Order ID: ' . $order->jubelio_order_id);

        $currentSalesOrder = $this->jubelio->getSalesOrderDetail($order->jubelio_order_id);

        if ($currentSalesOrder) {
            $updatePayload = $this->buildJubelioCancellationPayload($currentSalesOrder);
            $this->jubelio->createOrUpdateSalesOrder($updatePayload);
        } else {
            Log::error('Could not retrieve sales order details from Jubelio for order ID: ' . $order->jubelio_order_id);
        }
    }

    /**
     * Build the payload for cancelling a Jubelio sales order.
     *
     * @param array $currentSalesOrder
     * @return array
     */
    private function buildJubelioCancellationPayload(array $currentSalesOrder): array
    {
        $payload = [
            'salesorder_id' => $currentSalesOrder['salesorder_id'],
            'salesorder_no' => $currentSalesOrder['salesorder_no'],
            'contact_id' => $currentSalesOrder['contact_id'],
            'customer_name' => $currentSalesOrder['customer_name'],
            'transaction_date' => $currentSalesOrder['transaction_date'],
            'location_id' => $currentSalesOrder['location_id'],
            'source' => $currentSalesOrder['source'],
            'items' => [],
            'is_canceled' => true,
            'cancel_reason' => 'Cancelled by system zafrada',
            'cancel_reason_detail' => 'Invoice expired',
        ];

        foreach ($currentSalesOrder['items'] as $item) {
            $payload['items'][] = [
                'salesorder_detail_id' => $item['salesorder_detail_id'],
                'item_id' => $item['item_id'],
                'tax_id' => $item['tax_id'],
                'price' => (float) $item['price'],
                'unit' => $item['unit'],
                'qty_in_base' => (float) $item['qty_in_base'],
                'disc' => (float) $item['disc'],
                'disc_amount' => (float) $item['disc_amount'],
                'tax_amount' => (float) $item['tax_amount'],
                'amount' => (float) $item['amount'],
                'location_id' => $item['loc_id'],
                'serial_no' => $item['serial_no'],
                'description' => $item['description'],
                'shipper' => $currentSalesOrder['shipper'],
                'channel_order_detail_id' => $item['channel_order_detail_id'],
                'tracking_no' => '',
            ];
        }

        return $payload;
    }
}
