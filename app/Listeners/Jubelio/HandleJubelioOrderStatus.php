<?php

namespace App\Listeners\Jubelio;

use App\Events\Jubelio\JubelioOrderStatus;
use App\Models\Order;
use App\Models\User;
use App\Notifications\OrderStatusUpdatedNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class HandleJubelioOrderStatus implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(JubelioOrderStatus $event): void
    {
        $order = $event->order;
        $data = $event->data;

        $oldStatus = $order->status;
        $newStatus = strtolower($data['status']);
        if ($newStatus === 'canceled') {
            $newStatus = 'cancelled';
        }
        $newTrackingNumber = $data['tracking_number'] ?? null;

        // Prevent duplicate updates
        if ($order->status === $newStatus && (is_null($newTrackingNumber) || $order->tracking_number === $newTrackingNumber)) {
            Log::info('Jubelio webhook ignored: status and tracking number are already up to date.', ['salesorder_id' => $data['salesorder_id']]);
            return;
        }

        $updateData = ['status' => $newStatus];
        if ($newTrackingNumber) {
            $updateData['tracking_number'] = $newTrackingNumber;
        }
        $order->update($updateData);
        $this->sendNotifications($order, $oldStatus, $newStatus);

        Log::info('Jubelio order status updated successfully by listener.', [
            'order_id' => $order->id,
            'new_status' => $newStatus,
            'new_tracking_number' => $newTrackingNumber
        ]);
    }

    private function sendNotifications(Order $order, string $oldStatus, string $newStatus): void
    {
        $order->load('user');
        $recipients = collect();
        $admins = User::where('role', 'admin')->get();
        $recipients = $recipients->merge($admins);

        if ($order->user && !$order->user->isAdmin()) {
            $recipients->push($order->user);
        }
        $recipients = $recipients->unique('id');

        if ($recipients->isNotEmpty()) {
            Log::info('Sending order status update notifications to: ' . json_encode($recipients->pluck('id')->toArray()));
            Notification::send($recipients, new OrderStatusUpdatedNotification($order, $oldStatus, $newStatus, null));
        }
    }
}
