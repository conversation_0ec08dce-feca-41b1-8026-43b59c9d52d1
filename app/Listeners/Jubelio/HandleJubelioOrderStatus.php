<?php

namespace App\Listeners\Jubelio;

use App\Events\Jubelio\JubelioOrderStatus;
use App\Models\Order;
use App\Models\User;
use App\Notifications\OrderCancelledNotification;
use App\Notifications\OrderCompletedNotification;
use App\Notifications\OrderStatusUpdatedNotification;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class HandleJubelioOrderStatus implements ShouldQueue
{
    protected NotificationService $notificationService;

    /**
     * Create the event listener.
     */
    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the event.
     */
    public function handle(JubelioOrderStatus $event): void
    {
        $order = $event->order;
        $data = $event->data;

        $oldStatus = $order->status;
        $newStatus = strtolower($data['status']);
        if ($newStatus === 'canceled') {
            $newStatus = 'cancelled';
        }
        $newTrackingNumber = $data['tracking_number'] ?? null;

        // Prevent duplicate updates
        if ($order->status === $newStatus && (is_null($newTrackingNumber) || $order->tracking_number === $newTrackingNumber)) {
            Log::info('Jubelio webhook ignored: status and tracking number are already up to date.', ['salesorder_id' => $data['salesorder_id']]);
            return;
        }

        $updateData = ['status' => $newStatus];
        if ($newTrackingNumber) {
            $updateData['tracking_number'] = $newTrackingNumber;
        }
        $order->update($updateData);

        try {
            $this->sendNotifications($order, $oldStatus, $newStatus);
        } catch (\Exception $e) {
            Log::error('Failed to send notification for order status update.', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);
        }

        Log::info('Jubelio order status updated successfully by listener.', [
            'order_id' => $order->id,
            'new_status' => $newStatus,
            'new_tracking_number' => $newTrackingNumber
        ]);
    }

    private function sendNotifications(Order $order, string $oldStatus, string $newStatus): void
    {
        if ($newStatus === Order::STATUS_COMPLETED) {
            $order->load('user');

            // Notify the user who placed the order
            if ($order->user) {
                Log::info("Sending 'order_completed' notification to user {$order->user->id}");
                $this->notificationService->send(
                    $order->user,
                    new OrderCompletedNotification($order),
                    'order_completed'
                );
            }

            // Notify all admins with an in-app notification only
            $admins = User::where('role', 'admin')->get();
            foreach ($admins as $admin) {
                // Don't send another notification to an admin who is also the order owner
                if ($order->user && $admin->id === $order->user->id) {
                    continue;
                }

                Log::info("Sending 'order_completed' in-app notification to admin {$admin->id}");
                $this->notificationService->sendInAppOnly(
                    $admin,
                    new OrderCompletedNotification($order)
                );
            }
        } elseif ($newStatus === Order::STATUS_CANCELLED) {
            $order->load('user');

            // Notify the user who placed the order
            if ($order->user) {
                Log::info("Sending 'order_cancelled' notification to user {$order->user->id}");
                $this->notificationService->send(
                    $order->user,
                    new OrderCancelledNotification($order, 'Cancelled by admin.'),
                    'order_cancelled'
                );
            }

            // Notify all admins with an in-app notification only
            $admins = User::where('role', 'admin')->get();
            foreach ($admins as $admin) {
                // Don't send another notification to an admin who is also the order owner
                if ($order->user && $admin->id === $order->user->id) {
                    continue;
                }

                Log::info("Sending 'order_cancelled' in-app notification to admin {$admin->id}");
                $this->notificationService->sendInAppOnly(
                    $admin,
                    new OrderCancelledNotification($order, 'Cancelled by admin.')
                );
            }
        } else {
            $order->load('user');
            $recipients = collect();
            $admins = User::where('role', 'admin')->get();
            $recipients = $recipients->merge($admins);

            if ($order->user && !$order->user->isAdmin()) {
                $recipients->push($order->user);
            }
            $recipients = $recipients->unique('id');

            if ($recipients->isNotEmpty()) {
                Log::info('Sending order status update notifications to: ' . json_encode($recipients->pluck('id')->toArray()));
                Notification::send($recipients, new OrderStatusUpdatedNotification($order, $oldStatus, $newStatus, null));
            }
        }
    }
}
