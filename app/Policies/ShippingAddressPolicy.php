<?php

namespace App\Policies;

use App\Models\ShippingAddress;
use App\Models\User;

class ShippingAddressPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'reseller']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ShippingAddress $shippingAddress): bool
    {
        if ($user->role === 'admin') {
            return true;
        }

        if ($user->role === 'reseller') {
            return $user->id === $shippingAddress->user_id;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'reseller']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ShippingAddress $shippingAddress): bool
    {
        if ($user->role === 'admin') {
            return true;
        }

        if ($user->role === 'reseller') {
            return $user->id === $shippingAddress->user_id;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ShippingAddress $shippingAddress): bool
    {
        if ($user->role === 'admin') {
            return true;
        }

        if ($user->role === 'reseller') {
            return $user->id === $shippingAddress->user_id;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ShippingAddress $shippingAddress): bool
    {
        return $this->delete($user, $shippingAddress);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ShippingAddress $shippingAddress): bool
    {
        return $user->role === 'admin';
    }
}
