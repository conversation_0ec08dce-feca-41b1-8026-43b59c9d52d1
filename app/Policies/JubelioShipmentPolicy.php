<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;

class JubelioShipmentPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Undocumented function
     *
     * @param User $user
     * @return boolean
     */
    public function getRegions(User $user): bool
    {
        // Allow any authenticated user to get regions
        return true;
    }

    /**
     * Determine whether the user can get service categories.
     */
    public function getServiceCategories(User $user): bool
    {
        // Allow any authenticated user to get service categories
        return true;
    }
    /**
     * Determine whether the user can get rates.
     */
    public function getRates(User $user): bool
    {
        // Allow any authenticated user to get rates
        return true;
    }

    /**
     * Determine whether the user can get the shipment log.
     */
    public function getShipmentLog(User $user): bool
    {
        // Allow any authenticated user to get the shipment log
        return true;
    }
}
