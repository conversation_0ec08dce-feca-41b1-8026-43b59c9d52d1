<?php

namespace App\Policies;

use App\Models\Order;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class OrderPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'reseller']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Order $order): bool
    {
        // Admin can view all orders
        if ($user->role === 'admin') {
            return true;
        }

        // Reseller can only view their own orders
        if ($user->role === 'reseller') {
            return $user->id === $order->user_id;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'reseller']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Order $order): bool
    {
        // Admin can update all orders
        if ($user->role === 'admin') {
            return true;
        }

        // Reseller can only update their own orders
        if ($user->role === 'reseller') {
            return $user->id === $order->user_id;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Order $order): bool
    {
        // Admin can delete all orders
        if ($user->role === 'admin') {
            return true;
        }

        // Reseller can only delete their own orders
        if ($user->role === 'reseller') {
            return $user->id === $order->user_id;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Order $order): bool
    {
        // Admin can restore all orders
        if ($user->role === 'admin') {
            return true;
        }

        // Reseller can only restore their own orders
        if ($user->role === 'reseller') {
            return $user->id === $order->user_id;
        }

        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Order $order): bool
    {
        // Admin can force delete all orders
        if ($user->role === 'admin') {
            return true;
        }

        // Reseller can only force delete their own orders
        if ($user->role === 'reseller') {
            return $user->id === $order->user_id;
        }

        return false;
    }
}
