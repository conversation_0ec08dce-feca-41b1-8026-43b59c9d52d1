<?php

namespace App\Channels;

use App\Contracts\WahaNotification;
use App\Services\WahaService;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class WahaChannel
{
    /**
     * The Waha service instance.
     *
     * @var \App\Services\WahaService
     */
    protected WahaService $wahaService;

    /**
     * Create a new channel instance.
     *
     * @param  \App\Services\WahaService  $wahaService
     * @return void
     */
    public function __construct(WahaService $wahaService)
    {
        $this->wahaService = $wahaService;
    }

    /**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return void
     */
    public function send($notifiable, Notification $notification): void
    {
        // Ensure the notification is intended for WAHA and implements the correct contract.
        if (!$notification instanceof WahaNotification) {
            return;
        }

        $content = $notification->toWaha($notifiable);

        if (empty($content)) {
            Log::warning('WhatsApp notification content is empty.', ['notification' => get_class($notification)]);
            return;
        }

        $recipient = $this->getRecipient($notifiable, $notification);

        if (!$recipient) {
            Log::warning('WhatsApp notification recipient could not be determined.', ['notification' => get_class($notification)]);
            return;
        }

        $this->wahaService->sendTextMessage($recipient, $content);
    }

    /**
     * Get the recipient's phone number.
     *
     * @param  mixed  $notifiable
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return string|null
     */
    protected function getRecipient($notifiable, Notification $notification): ?string
    {
        if (method_exists($notifiable, 'routeNotificationForWaha')) {
            return $notifiable->routeNotificationForWaha($notification);
        }

        // Fallback to a common property like 'phone' or 'phone_number'
        return $notifiable->phone_number ?? $notifiable->phone ?? null;
    }
}
