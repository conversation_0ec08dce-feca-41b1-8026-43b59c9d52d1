<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Services\AirwaybillService;
use App\Services\JubelioShipment;
use App\Services\JubelioShipmentAirwaybillService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestJubelioShipment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otoapi:test-jubelio-shipment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the Jubelio Shipment API integration';

    /**
     * Execute the console command.
     */
    public function handle(JubelioShipment $jubelioShipment, AirwaybillService $airwaybillService, JubelioShipmentAirwaybillService $jubelioShipmentAirwaybillService)
    {
        $this->info('Select a Jubelio Shipment API test to run:');
        $test = $this->choice(
            'Which endpoint would you like to test?',
            ['Test JubelioShipmentAirwaybillService', 'Test AirwaybillService', 'List Shipments', 'Get Service Categories', 'Get Shipment Cancel Reasons', 'Get Address List', 'Get Regions', 'Get Shipment Log', 'Cancel'],
            0
        );

        switch ($test) {
            case 'Test JubelioShipmentAirwaybillService':
                $this->testJubelioShipmentAirwaybillService($jubelioShipmentAirwaybillService);
                break;
            case 'Test AirwaybillService':
                $this->testAirwaybillService($airwaybillService);
                break;
            case 'List Shipments':
                $this->testListShipments($jubelioShipment);
                break;
            case 'Get Service Categories':
                $this->testGetServiceCategories($jubelioShipment);
                break;
            case 'Get Shipment Cancel Reasons':
                $this->testGetShipmentCancelReasons($jubelioShipment);
                break;
            case 'Get Address List':
                $this->testGetAddressList($jubelioShipment);
                break;
            case 'Get Regions':
                $this->testGetRegions($jubelioShipment);
                break;
           case 'Get Shipment Log':
               $this->testGetShipmentLog($jubelioShipment);
               break;
            case 'Cancel':
                $this->info('Operation cancelled.');
                return 0;
        }

        return 0;
    }

    protected function testListShipments(JubelioShipment $jubelioShipment)
    {
        $this->info('Testing List Shipments endpoint...');
        $shipments = $jubelioShipment->listShipments();

        if ($shipments) {
            $this->info('Successfully retrieved shipments. Check the logs for the response.');
        } else {
            $this->error('Failed to retrieve shipments. Check the logs for more details.');
        }
    }

    protected function testGetAddressList(JubelioShipment $jubelioShipment)
    {
        $this->info('Testing Get Address List endpoint...');
        $addresses = $jubelioShipment->getAddressList();

        if ($addresses) {
            $this->info('Successfully retrieved address list. Check the logs for the response.');
        } else {
            $this->error('Failed to retrieve address list. Check the logs for more details.');
        }
    }

    protected function testGetServiceCategories(JubelioShipment $jubelioShipment)
    {
        $this->info('Testing Get Service Categories endpoint...');
        $categories = $jubelioShipment->getServiceCategories();

        if ($categories) {
            $this->info('Successfully retrieved service categories. Check the logs for the response.');
        } else {
            $this->error('Failed to retrieve service categories. Check the logs for more details.');
        }
    }

    protected function testGetShipmentCancelReasons(JubelioShipment $jubelioShipment)
    {
        $this->info('Testing Get Shipment Cancel Reasons endpoint...');
        $reasons = $jubelioShipment->getShipmentCancelReasons();

        if ($reasons) {
            $this->info('Successfully retrieved shipment cancel reasons. Check the logs for the response.');
        } else {
            $this->error('Failed to retrieve shipment cancel reasons. Check the logs for more details.');
        }
    }

    protected function testGetRegions(JubelioShipment $jubelioShipment)
    {
        $this->info('Testing Get Regions endpoint...');
        $name = 'Badang, Ngoro';
        $this->info("Searching for region: '{$name}'");
        $regions = $jubelioShipment->getRegions($name);

        if ($regions) {
            $this->info('Successfully retrieved regions. Check the logs for the response.');
        } else {
            $this->error('Failed to retrieve regions. Check the logs for more details.');
        }
    }

    protected function testAirwaybillService(AirwaybillService $airwaybillService)
    {
        $this->info('Testing AirwaybillService...');
        $order = Order::where('shipping_type', Order::SHIPPING_TYPE_EXPEDITION)->latest()->first();

        if (!$order) {
            $this->error('No orders found in the database.');
            return;
        }

        $this->info("Using latest order: #{$order->id}");
        $airwaybillService->handleExpedition($order);
        $this->info('AirwaybillService handleExpedition method executed. Check the logs for details.');
    }

    protected function testJubelioShipmentAirwaybillService(JubelioShipmentAirwaybillService $jubelioShipmentAirwaybillService)
    {
        $this->info('Testing JubelioShipmentAirwaybillService...');
        $order = Order::where('shipping_type', Order::SHIPPING_TYPE_JUBELIO_SHIPMENT)->latest()->first();

        if (!$order) {
            $this->error('No orders with shipping_type jubelio_shipment found in the database.');
            return;
        }

        $this->info("Using latest order: #{$order->id}");
        $result = $jubelioShipmentAirwaybillService->handle($order);

        if ($result['success']) {
            $this->info('JubelioShipmentAirwaybillService handle method executed successfully. Check the logs for details.');
        } else {
            $this->error('JubelioShipmentAirwaybillService handle method failed. Check the logs for details.');
        }
        $this->info('Result: ' . json_encode($result));
    }

    protected function testGetShipmentLog(JubelioShipment $jubelioShipment)
    {
        $this->info('Testing Get Shipment Log endpoint...');
        $order = Order::where('shipping_type', Order::SHIPPING_TYPE_JUBELIO_SHIPMENT)->latest()->first();

        if (!$order) {
            $this->error('No orders with shipping_type jubelio_shipment found in the database.');
            return;
        }

        $this->info("Using latest order: #{$order->id}");

        $etc = $order->etc;
        $shipmentId = $etc['jubelio_shipment']['shipment_id'] ?? null;

        if (!$shipmentId) {
            $this->error('Shipment ID not found for this order.');
            return;
        }

        $this->info("Getting log for shipment ID: {$shipmentId}");
        $log = $jubelioShipment->getShipmentLog($shipmentId);

        if ($log) {
            $this->info('Successfully retrieved shipment log. Check the logs for the response.');
            Log::info('Shipment Log:', $log);
        } else {
            $this->error('Failed to retrieve shipment log. Check the logs for more details.');
        }
    }
}
