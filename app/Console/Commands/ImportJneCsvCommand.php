<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Services\JneCsvImportService;


class ImportJneCsvCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otoapi:import-jne {type? : The type of data to import (branches, origins, destinations)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import JNE data from CSV files located in database/seeders/data/jne';

    protected JneCsvImportService $importService;

    /**
     * Create a new command instance.
     *
     * @param JneCsvImportService $importService
     */
    public function __construct(JneCsvImportService $importService)
    {
        parent::__construct();
        $this->importService = $importService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');
        $this->importService->setOutput($this->output);

        if (!$type) {
            $this->info('Select a type to run:');
            $type = $this->choice(
                'Which type would you like to run?',
                ['branches', 'origins', 'destinations', 'cancel'],
                0
            );
        }


        switch ($type) {
            case 'branches':
                $filePath = database_path('seeders/data/jne/branch.csv');
                $this->importWithProgressBar($filePath, 'importBranches');
                break;
            case 'origins':
                $filePath = database_path('seeders/data/jne/list_origin.csv');
                $this->importWithProgressBar($filePath, 'importOrigins');
                break;
            case 'destinations':
                $filePath = database_path('seeders/data/jne/list_dest.csv');
                $this->importWithProgressBar($filePath, 'importDestinations');
                break;
            case 'cancel':
                $this->info('import cancel.');
                return 0;
            default:
                $this->error("Invalid import type '{$type}'. Please use 'branches', 'origins', or 'destinations'.");
                return 1;
        }

        return 0;
    }

    /**
     * Helper to run import with a progress bar.
     */
    protected function importWithProgressBar(string $filePath, string $method): void
    {
        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return;
        }

        $this->info("Counting rows in {$filePath}...");
        $totalRows = \Spatie\SimpleExcel\SimpleExcelReader::create($filePath)->getRows()->count();

        $this->output->newLine();
        $progressBar = $this->output->createProgressBar($totalRows);
        $progressBar->start();

        $this->importService->{$method}($filePath, function ($rowCount) use ($progressBar) {
            $progressBar->advance($rowCount);
        });

        $progressBar->finish();
        $this->output->newLine(2);
    }
}
