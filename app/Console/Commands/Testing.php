<?php

namespace App\Console\Commands;

use App\Events\Jubelio\JubelioOrderStatus;
use App\Events\Xendit\XenditInvoiceExpired;
use App\Models\Order;
use App\Models\User;
use App\Notifications\OrderCancelledNotification;
use App\Notifications\OrderCompletedNotification;
use App\Notifications\OrderPaidNotification;
use App\Services\Jne;
use App\Services\Jubelio;
use App\Services\NotificationService;
use App\Services\XenditService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Password;

class Testing extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otoapi:testing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Testing for otoapi';

    /**
     * Execute the console command.
     */
    public function handle(Jubelio $jubelio, XenditService $xenditService, NotificationService $notificationService, Jne $jne)
    {
        $this->info('Select a test to run:');
        $test = $this->choice(
            'Which test would you like to run?',
            ['Send Test Reset Password Email', 'Jubelio Ready to Pick', 'Send Test Paid Notification', 'Send Test Completed Notification', 'Send Test Cancelled Notification', 'JNE Generate CNote', 'JNE Get Price'],
            0
        );

        switch ($test) {
            case 'Send Test Reset Password Email':
                $this->testSendResetPasswordEmail();
                break;
            case 'Jubelio Ready to Pick':
                $jubelio->setReadyToPick([872]);
                $this->info('Jubelio ready to pick test executed.');
                break;
            case 'Send Test Paid Notification':
                $this->testSendPaidNotification($notificationService);
                break;
            case 'Send Test Completed Notification':
                $this->testSendCompletedNotification();
                break;
            case 'Send Test Cancelled Notification':
                $this->testSendCancelledNotification($notificationService);
                break;
            case 'Send Test Completed Notification using notification service':
                $this->testSendCompletedNotification2($notificationService);
                break;
            case 'JNE Generate CNote':
                $this->testJneGenerateCnote($jne);
                break;
            case 'JNE Get Price':
                $this->testGetJnePrice($jne);
                break;
        }
    }

    protected function testSendResetPasswordEmail()
    {
        $email = $this->ask('Enter the email address to send the test to:');

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('Invalid email address.');
            return;
        }

        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User with email '{$email}' not found.");
            return;
        }

        $this->info("Sending password reset link to {$user->name} ({$user->email})...");

        // Create a password reset token for the user
        $token = Password::createToken($user);

        // Send the notification
        $user->sendPasswordResetNotification($token);

        $this->info('Test email sent successfully!');
    }


    protected function testSendPaidNotification(NotificationService $notificationService)
    {
        $this->info('Sending test paid notification...');

        $order = Order::latest()->first();

        if (!$order) {
            $this->error('No orders found in the database.');
            return;
        }

        $user = $order->user;

        if (!$user) {
            $this->error("Order #{$order->id} does not have an associated user.");
            return;
        }

        $this->info("Sending paid notification for Order #{$order->id} to {$user->name} ({$user->email})...");

        $notificationService->send(
            $user,
            new OrderPaidNotification($order),
            'order_paid'
        );

        $this->info('Test paid notification sent successfully!');
    }

    protected function testSendCompletedNotification2(NotificationService $notificationService)
    {
        $this->info('Sending test completed notification...');
        $order = Order::latest()->first();
        if (!$order) {
            $this->error('No orders found in the database.');
            return;
        }

        $user = $order->user;

        if (!$user) {
            $this->error("Order #{$order->id} does not have an associated user.");
            return;
        }

        $order->expedition = [
            'name' => 'POS Indonesia (POS) - Pos Reguler',
            'code' => 'POS',
            'service' => 'Pos Reguler',
            'description' => '240 • Est. delivery: 4 day days • Includes Rp 500 insurance',
            'cost' => 12000,
            'etd' => '4'
        ];

        $order->tracking_number = 'POS' . str_pad((string) rand(1, 999999999), 9, '0', STR_PAD_LEFT);
        $order->status = Order::STATUS_COMPLETED;
        $order->save();

        $this->info("Sending completed notification for Order #{$order->id} to {$user->name} ({$user->email})...");
        $this->info("Tracking Number: {$order->tracking_number}");
        $this->info("Expedition: {$order->expedition['name']}");

        $notificationService->send(
            $order->user,
            new OrderCompletedNotification($order),
            'order_completed'
        );

        $this->info('Test completed notification sent successfully!');
    }

    protected function testSendCompletedNotification()
    {
        $this->info('Testing completed order event...');

        $order = Order::latest()->first();

        if (!$order) {
            $this->error('No orders found in the database.');
            return;
        }

        $this->info("Using Order #{$order->id} for the test.");

        // Create dummy webhook data
        $dummyWebhookData = [
            'status' => 'completed',
            'tracking_number' => 'TEST' . str_pad((string) rand(1, 999999999), 9, '0', STR_PAD_LEFT),
            'salesorder_id' => $order->jubelio_order_id,
        ];

        $this->info("Dispatching JubelioOrderStatus event with status: 'completed'");
        $this->info("Dummy Tracking Number: {$dummyWebhookData['tracking_number']}");

        // Dispatch the event
        JubelioOrderStatus::dispatch($order, $dummyWebhookData);

        $this->info('Test event dispatched successfully! Check the queue and logs for results.');
    }

    protected function testJneGenerateCnote(Jne $jne)
    {
        $this->info('Testing JNE Generate CNote...');

        $data = [
           "OLSHOP_BRANCH" => "KDR000",
           "OLSHOP_CUST" => '10505500',
           "OLSHOP_ORDERID" => 'COBACOBA3',
           "OLSHOP_SHIPPER_NAME" => "Zafrada Store",
           "OLSHOP_SHIPPER_ADDR1" => "Jalan Masjid No 35",
           "OLSHOP_SHIPPER_ADDR2" => "Karangrejo",
           "OLSHOP_SHIPPER_ADDR3" => "Ngasem",
           "OLSHOP_SHIPPER_CITY" => "Kediri",
           "OLSHOP_SHIPPER_REGION" => "Jawa Timur",
           "OLSHOP_SHIPPER_ZIP" => "64182",
           "OLSHOP_SHIPPER_PHONE" => "082143724860",
           "OLSHOP_RECEIVER_NAME" => "Zuhri",
           "OLSHOP_RECEIVER_ADDR1" => "Jl Bupati Ismail No 80  RT3 RW1 Badang",
           "OLSHOP_RECEIVER_ADDR2" => "Badang",
           "OLSHOP_RECEIVER_ADDR3" => "Ngoro",
           "OLSHOP_RECEIVER_CITY" => "KAB. JOMBANG",
           "OLSHOP_RECEIVER_REGION" => "JAWA TIMUR",
           "OLSHOP_RECEIVER_ZIP" => "61473",
           "OLSHOP_RECEIVER_PHONE" => "085336383975",
           "OLSHOP_QTY" => 1,
           "OLSHOP_WEIGHT" => 1,
           "OLSHOP_GOODSDESC" => "test",
           "OLSHOP_GOODSVALUE" => "72500.00",
           "OLSHOP_GOODSTYPE" => 2,
           "OLSHOP_INST" => "Take care the products",
           "OLSHOP_INS_FLAG" => "N",
           "OLSHOP_ORIG" => "KDR10000",
           "OLSHOP_DEST" => "MJK10112",
           "OLSHOP_SERVICE" => "REG23",
           "OLSHOP_COD_FLAG" => "N",
           "OLSHOP_COD_AMOUNT" => 0,
        ];

        $jne->generateAirwaybillWithGuzzle($data);

        $this->info('JNE Generate CNote test executed. Check the logs for results.');
    }

    protected function testGetJnePrice(Jne $jne)
    {
        $this->info('Testing JNE Get Price...');

        $data = [
            'from' => 'KDR10000',
            'thru' => 'MJK10112',
            'weight' => 1,
        ];

        $jne->getPricesWithGuzzle($data);

        $this->info('JNE Get Price test executed. Check the logs for results.');
    }

    protected function testSendCancelledNotification(NotificationService $notificationService)
    {
        $this->info('Sending test cancelled notification...');

        $order = Order::latest()->first();

        if (!$order) {
            $this->error('No orders found in the database.');
            return;
        }

        $user = $order->user;

        if (!$user) {
            $this->error("Order #{$order->id} does not have an associated user.");
            return;
        }

        $this->info("Sending cancelled notification for Order #{$order->id} to {$user->name} ({$user->email})...");

        $notificationService->send(
            $user,
            new OrderCancelledNotification($order),
            'order_cancelled'
        );

        $this->info('Test cancelled notification sent successfully!');
    }
}
