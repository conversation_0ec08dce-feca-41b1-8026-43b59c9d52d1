<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Models\User;
use App\Notifications\OrderPaidNotification;
use App\Notifications\OrderCompletedNotification;
use App\Services\Jubelio;
use App\Services\NotificationService;
use App\Services\XenditService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Password;

class Testing extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otoapi:testing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Testing for otoapi';

    /**
     * Execute the console command.
     */
    public function handle(Jubelio $jubelio, XenditService $xenditService, NotificationService $notificationService)
    {
        $this->info('Select a test to run:');
        $test = $this->choice(
            'Which test would you like to run?',
            ['Send Test Reset Password Email', 'Jubelio Ready to Pick', 'Send Test Paid Notification', 'Send Test Completed Notification'],
            0
        );

        switch ($test) {
            case 'Send Test Reset Password Email':
                $this->testSendResetPasswordEmail();
                break;
            case 'Jubelio Ready to Pick':
                $jubelio->setReadyToPick([872]);
                $this->info('Jubelio ready to pick test executed.');
                break;
            case 'Send Test Paid Notification':
                $this->testSendPaidNotification($notificationService);
                break;
            case 'Send Test Completed Notification':
                $this->testSendCompletedNotification($notificationService);
                break;
        }
    }

    protected function testSendResetPasswordEmail()
    {
        $email = $this->ask('Enter the email address to send the test to:');

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('Invalid email address.');
            return;
        }

        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User with email '{$email}' not found.");
            return;
        }

        $this->info("Sending password reset link to {$user->name} ({$user->email})...");

        // Create a password reset token for the user
        $token = Password::createToken($user);

        // Send the notification
        $user->sendPasswordResetNotification($token);

        $this->info('Test email sent successfully!');
    }

    protected function testSendPaidNotification(NotificationService $notificationService)
    {
        $this->info('Sending test paid notification...');

        $order = Order::latest()->first();

        if (!$order) {
            $this->error('No orders found in the database.');
            return;
        }

        $user = $order->user;

        if (!$user) {
            $this->error("Order #{$order->id} does not have an associated user.");
            return;
        }

        $this->info("Sending paid notification for Order #{$order->id} to {$user->name} ({$user->email})...");

        $notificationService->send(
            $user,
            new OrderPaidNotification($order),
            'order_paid'
        );

        $this->info('Test paid notification sent successfully!');
    }

    protected function testSendCompletedNotification(NotificationService $notificationService)
    {
        $this->info('Sending test completed notification...');

        $order = Order::latest()->first();

        if (!$order) {
            $this->error('No orders found in the database.');
            return;
        }

        $user = $order->user;

        if (!$user) {
            $this->error("Order #{$order->id} does not have an associated user.");
            return;
        }

        // Set some example expedition and tracking data for testing
        $order->expedition = [
            'name' => 'POS Indonesia (POS) - Pos Reguler',
            'code' => 'POS',
            'service' => 'Pos Reguler',
            'description' => '240 • Est. delivery: 4 day days • Includes Rp 500 insurance',
            'cost' => 12000,
            'etd' => '4'
        ];
        $order->tracking_number = 'POS' . str_pad(rand(1, 999999999), 9, '0', STR_PAD_LEFT);
        $order->status = Order::STATUS_COMPLETED;
        $order->save();

        $this->info("Sending completed notification for Order #{$order->id} to {$user->name} ({$user->email})...");
        $this->info("Tracking Number: {$order->tracking_number}");
        $this->info("Expedition: {$order->expedition['name']}");

        $notificationService->send(
            $user,
            new OrderCompletedNotification($order),
            'order_completed'
        );

        $this->info('Test completed notification sent successfully!');
    }
}
