<?php

namespace App\Console\Commands;

use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateOrderStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otoapi:order-update {order_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update the status of an order';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $orderId = $this->argument('order_id');
        $order = Order::find($orderId);

        if (!$order) {
            $this->error("Order with ID {$orderId} not found.");
            Log::error("Attempted to update a non-existent order with ID: {$orderId}");
            return 1;
        }

        $this->info("Current status of order #{$order->id}: {$order->status}");

        $statuses = Order::getStatuses();
        $newStatus = $this->choice(
            'Select the new status for the order:',
            array_merge($statuses, ['cancel'])
        );

        if ($newStatus === 'cancel') {
            $this->info('Operation cancelled.');
            Log::info("Order update operation cancelled for order #{$order->id}.");
            return 0;
        }

        if (!in_array($newStatus, $statuses)) {
            $this->error("Invalid status selected.");
            Log::warning("Invalid status '{$newStatus}' selected for order #{$order->id}.");
            return 1;
        }

        $order->status = $newStatus;
        $order->save();

        $this->info("Order #{$order->id} status has been updated to '{$newStatus}'.");
        Log::info("Order #{$order->id} status updated from '{$order->getOriginal('status')}' to '{$newStatus}'.");

        return 0;
    }
}
