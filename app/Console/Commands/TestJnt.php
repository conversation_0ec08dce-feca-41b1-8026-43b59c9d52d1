<?php

namespace App\Console\Commands;

use App\Services\Jnt;
use Illuminate\Console\Command;

class TestJnt extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otoapi:test-jnt';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the JNT API integration';

    /**
     * Execute the console command.
     */
    public function handle(Jnt $jnt)
    {
        $this->info('Select a JNT API test to run:');
        $test = $this->choice(
            'Which endpoint would you like to test?',
            ['Create AWB', 'Track AWB', 'Check Tariff', 'Cancel AWB', 'Cancel'],
            0
        );

        switch ($test) {
            case 'Create AWB':
                $this->testCreateAwb($jnt);
                break;
            case 'Track AWB':
                $this->testTrackAwb($jnt);
                break;
            case 'Check Tariff':
                $this->testCheckTariff($jnt);
                break;
            case 'Cancel AWB':
                $this->testCancelAwb($jnt);
                break;
            case 'Cancel':
                $this->info('Operation cancelled.');
                return 0;
        }

        return 0;
    }

    protected function testCreateAwb(Jnt $jnt)
    {
        $this->info('Testing Create AWB endpoint...');
        $payload = [
            'orderid' => 'ORDERID-' . time(),
            'shipper_name' => 'PENGIRIM',
            'shipper_contact' => 'PENGIRIM',
            'shipper_phone' => '+628123456789',
            'shipper_addr' => 'JL. Pengirim no.88, RT/RW:001/010, Pluit',
            'origin_code' => 'JKT',
            'receiver_name' => 'PENERIMA',
            'receiver_phone' => '+62812348888',
            'receiver_addr' => 'JL. Penerima no.1, RT/RW:04/07, Sidoarjo',
            'receiver_zip' => '40123',
            'destination_code' => 'JKT',
            'receiver_area' => 'JKT001',
            'qty' => '1',
            'weight' => '1',
            'goodsdesc' => 'TESTING!!',
            'servicetype' => '1',
            'insurance' => '122',
            'orderdate' => now()->format('Y-m-d H:i:s'),
            'item_name' => 'topi',
            'cod' => '120000',
            'sendstarttime' => now()->format('Y-m-d H:i:s'),
            'sendendtime' => now()->addHours(5)->format('Y-m-d H:i:s'),
            'expresstype' => '1',
            'goodsvalue' => '1000',
        ];

        $response = $jnt->createAwb($payload);

        if ($response) {
            $this->info('Successfully called Create AWB endpoint. Check the logs for the response.');
            $this->line(json_encode($response, JSON_PRETTY_PRINT));
        } else {
            $this->error('Failed to call Create AWB endpoint. Check the logs for more details.');
        }
    }

    protected function testTrackAwb(Jnt $jnt)
    {
        $this->info('Testing Track AWB endpoint...');
        $awb = $this->ask('Enter the AWB number to track', 'JD1234567890');
        $response = $jnt->trackAwb($awb);

        if ($response) {
            $this->info('Successfully called Track AWB endpoint. Check the logs for the response.');
            $this->line(json_encode($response, JSON_PRETTY_PRINT));
        } else {
            $this->error('Failed to call Track AWB endpoint. Check the logs for more details.');
        }
    }

    protected function testCheckTariff(Jnt $jnt)
    {
        $this->info('Testing Check Tariff endpoint...');
        $payload = [
            'weight' => '1',
            'sendSiteCode' => 'JAKARTA',
            'destAreaCode' => 'KALIDERES',
            'productType' => 'EZ',
        ];

        $response = $jnt->checkTariff($payload);

        if ($response) {
            $this->info('Successfully called Check Tariff endpoint. Check the logs for the response.');
            $this->line(json_encode($response, JSON_PRETTY_PRINT));
        } else {
            $this->error('Failed to call Check Tariff endpoint. Check the logs for more details.');
        }
    }

    protected function testCancelAwb(Jnt $jnt)
    {
        $this->info('Testing Cancel AWB endpoint...');
        $orderId = $this->ask('Enter the Order ID to cancel', 'ORDERID-12345678');
        $payload = [
            'orderid' => $orderId,
            'remark' => 'Canceled by E-Commerce',
        ];

        $response = $jnt->cancelAwb($payload);

        if ($response) {
            $this->info('Successfully called Cancel AWB endpoint. Check the logs for the response.');
            $this->line(json_encode($response, JSON_PRETTY_PRINT));
        } else {
            $this->error('Failed to call Cancel AWB endpoint. Check the logs for more details.');
        }
    }
}
