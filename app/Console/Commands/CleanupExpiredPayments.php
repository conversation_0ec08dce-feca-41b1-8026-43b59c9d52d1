<?php

namespace App\Console\Commands;

use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CleanupExpiredPayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otoapi:cleanup-expired-payments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mark expired payments and orders as expired';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $expiredPayments = Payment::where('status', 'pending')
            ->where('expires_at', '<', Carbon::now())
            ->get();

        foreach ($expiredPayments as $payment) {
            $payment->update(['status' => 'expired']);
            $payment->order->update(['status' => 'expired']);

            $this->info("Marked payment {$payment->external_id} as expired");
        }

        $this->info("Processed {$expiredPayments->count()} expired payments");

        return Command::SUCCESS;
    }
}
