<?php

namespace App\Console\Commands;

use App\Services\XenditService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SetXenditWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otoapi:set-xendit-webhook';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set the Xendit invoice webhook URL';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(XenditService $xenditService)
    {
        $this->info('Setting Xendit invoice webhook URL...');

        try {
            $xenditService->setInvoiceWebhookUrl();
            $this->info('Successfully set Xendit invoice webhook URL.');
            Log::info('Xendit webhook URL set successfully via Artisan command.');
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to set Xendit invoice webhook URL.');
            $this->error($e->getMessage());
            Log::error('Failed to set Xendit webhook URL via Artisan command.', ['error' => $e->getMessage()]);
            return Command::FAILURE;
        }
    }
}
