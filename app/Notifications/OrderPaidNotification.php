<?php

namespace App\Notifications;

use App\Models\Order;
use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class OrderPaidNotification extends BaseNotification implements ShouldQueue
{
    use Queueable;

    protected $order;

    protected $payment;

    private $frontend_base_url;

    /**
     * Create a new notification instance.
     *
     * @param Order $order
     */
    public function __construct(Order $order)
    {
        if ($order->relationLoaded('items') && $order->relationLoaded('user')) {
            $this->order = $order;
        } else {
            $this->order = $order->load(['items', 'user']);
        }

        $this->payment = $order->latestPayment;
        $this->frontend_base_url = config('frontend.otoresell_url');
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        $url = $this->frontend_base_url . '/transactions/' . $this->order->id;

        return (new MailMessage)
            ->subject('Pembayaran Berhasil untuk Pesanan #' . $this->order->id)
            ->markdown('mail.order.paid', [
                'order' => $this->order,
                'payment' => $this->payment,
                'url' => $url
            ]);
    }

    /**
     * Get the array representation for the database channel (in-app).
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toDatabase($notifiable): array
    {
        $isOrderOwner = $notifiable->id === $this->order->user_id;
        $isAdmin = $notifiable->role === 'admin';

        // Different messages for different user types
        if ($isOrderOwner && !$isAdmin) {
            // Message for the customer
            $message = "Payment for your order #{$this->order->id} has been successful.";
        } else {
            // Message for admin
            $orderOwnerName = $this->order->user->name ?? 'Unknown';
            $message = "Payment received for order #{$this->order->id} from {$orderOwnerName}.";
        }

        return [
            'title' => 'Order Paid',
            'message' => $message,
            'type' => 'order',
            'action' => 'paid', // A new action type for payment
            'order' => [
                'id' => $this->order->id,
                'jubelio_order_id' => $this->order->jubelio_order_id,
                'new_status' => Order::STATUS_PAID, // The status is now 'paid'
                'grand_total' => $this->order->grand_total,
                'updated_at' => $this->order->updated_at->toISOString(),
            ],
            'updated_by' => null, // Payment is usually from a system/user action, not an admin update
            'order_owner' => [
                'id' => $this->order->user_id,
                'name' => $this->order->user->name ?? 'Unknown',
            ],
        ];
    }

    /**
     * Get the array representation of the notification (default/fallback).
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable): array
    {
        // This can be kept simple for other channels or as a fallback
        return [
            'order_id' => $this->order->id,
            'message' => 'Order #' . $this->order->id . ' has been paid.',
            'url' => $this->frontend_base_url . '/transactions/' . $this->order->id,
        ];
    }
}
