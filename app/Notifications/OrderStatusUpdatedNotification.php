<?php

namespace App\Notifications;

use App\Models\Order;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class OrderStatusUpdatedNotification extends Notification
{
    use Queueable;

    public $order;
    public $oldStatus;
    public $newStatus;
    public $updatedBy;

    /**
     * Create a new notification instance.
     */
    public function __construct(Order $order, string $oldStatus, string $newStatus, ?User $updatedBy)
    {
        $this->order = $order;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
        $this->updatedBy = $updatedBy;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    // /**
    //  * Get the mail representation of the notification.
    //  */
    // public function toMail(object $notifiable): MailMessage
    // {
    //     return (new MailMessage)
    //         ->line('The introduction to the notification.')
    //         ->action('Notification Action', url('/'))
    //         ->line('Thank you for using our application!');
    // }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $isOrderOwner = $notifiable->id === $this->order->user_id;
        $isAdmin = $notifiable->role === 'admin';

        // Different messages for different user types
        if ($isOrderOwner && !$isAdmin) {
            // Message for reseller who created the order
            $message = "The status of your order #{$this->order->id} has been updated to '{$this->newStatus}'.";
        } else {
            // Message for admin
            $orderOwnerName = $this->order->user->name ?? 'Unknown';
            $updatedByName = $this->updatedBy ? $this->updatedBy->name : 'System';
            $message = "Status for order #{$this->order->id} ({$this->order->jubelio_order_id}) by {$orderOwnerName} was updated from '{$this->oldStatus}' to '{$this->newStatus}' by {$updatedByName}.";
        }

        return [
            'title' => $this->getStatusTitle($this->newStatus),
            'message' => $message,
            'type' => 'order',
            'action' => 'status_updated',
            'order' => [
                'id' => $this->order->id,
                'jubelio_order_id' => $this->order->jubelio_order_id,
                'old_status' => $this->oldStatus,
                'new_status' => $this->newStatus,
                'grand_total' => $this->order->grand_total,
                'updated_at' => $this->order->updated_at->toISOString(),
            ],
            'updated_by' => $this->updatedBy ? [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->name,
                'role' => $this->updatedBy->role,
            ] : null,
            'order_owner' => [
                'id' => $this->order->user_id,
                'name' => $this->order->user->name ?? 'Unknown',
            ],
        ];
    }

    /**
     * Get title based on order status
     */
    private function getStatusTitle(string $status): string
    {
        return match($status) {
            Order::STATUS_PENDING => 'Order Pending',
            Order::STATUS_CONFIRMED => 'Order Confirmed',
            Order::STATUS_PROCESSING => 'Order Processing',
            Order::STATUS_SHIPPED => 'Order Shipped',
            Order::STATUS_DELIVERED => 'Order Delivered',
            Order::STATUS_CANCELLED => 'Order Cancelled',
            Order::STATUS_REFUNDED => 'Order Refunded',
            default => 'Order Status Updated',
        };
    }
}
