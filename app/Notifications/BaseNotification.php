<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class BaseNotification extends Notification
{
    use Queueable;

    protected $channel;

    /**
     * Set the notification channel.
     *
     * @param string $channel
     * @return $this
     */
    public function setChannel(string $channel): self
    {
        $this->channel = $channel;
        return $this;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable): array
    {
        // If a specific channel was set, use it.
        // Otherwise, default to 'database'.
        return $this->channel ? [$this->channel] : ['database'];
    }
}
