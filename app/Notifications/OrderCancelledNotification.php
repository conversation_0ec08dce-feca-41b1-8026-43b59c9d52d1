<?php

namespace App\Notifications;

use App\Channels\WahaChannel;
use App\Contracts\WahaNotification;
use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Carbon;
use Log;

class OrderCancelledNotification extends BaseNotification implements ShouldQueue, WahaNotification
{
    use Queueable;

    protected $order;

    protected ?string $reason;

    private $frontend_base_url;

    /**
     * Create a new notification instance.
     *
     * @param Order $order
     * @param string|null $reason
     */
    public function __construct(Order $order, ?string $reason = null)
    {
        if ($order->relationLoaded('items') && $order->relationLoaded('user')) {
            $this->order = $order;
        } else {
            $this->order = $order->load(['items', 'user']);
        }

        $this->reason = $reason;
        $this->frontend_base_url = config('frontend.otoresell_url');
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        $url = $this->frontend_base_url . '/transactions/' . $this->order->id;

        return (new MailMessage)
            ->subject('Pesanan Dibatalkan - Order #' . $this->order->id)
            ->markdown('mail.order.cancelled', [
                'order' => $this->order,
                'reason' => $this->reason,
                'url' => $url
            ]);
    }

    /**
     * Get the Waha representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return string
     */
    public function toWaha($notifiable): string
    {
        $userName = $this->order->user->name;
        $orderId = $this->order->id;
        $items = $this->order->items;

        $message = "Assalaamu'alaikum {$userName} 🙂\n\n";
        $message .= "Mohon maaf, pesanan Anda dengan nomor: {$orderId} telah dibatalkan.\n\n";

        if ($this->reason) {
            $message .= "Alasan: {$this->reason}\n\n";
        }

        $message .= "Berikut daftar produk yang dibatalkan:\n\n";

        foreach ($items as $key => $item) {
            $itemNumber = $key + 1;
            $productName = "*{$item->name}*";
            $quantity = $item->quantity;
            $price = number_format($item->price, 0, ',', '.');
            $totalPrice = number_format($item->total_price, 0, ',', '.');
            $message .= "{$itemNumber}. {$productName} {$quantity} x Rp. {$price} = Rp. *{$totalPrice}*\n";
        }

        $subTotal = number_format($this->order->sub_total, 0, ',', '.');
        $shippingCost = number_format($this->order->shipping_cost, 0, ',', '.');
        $totalDiscount = number_format($this->order->total_discount, 0, ',', '.');
        $insuranceCost = number_format($this->order->insurance_cost, 0, ',', '.');
        $grandTotal = number_format($this->order->grand_total, 0, ',', '.');

        $message .= "\nSubtotal: Rp. *{$subTotal}*\n";
        $message .= "Ongkir: Rp. *{$shippingCost}*\n";
        if ($this->order->total_discount > 0) {
            $message .= "Total Diskon: -Rp. *{$totalDiscount}*\n";
        }
        if ($this->order->insurance_cost > 0) {
            $message .= "Asuransi: Rp. *{$insuranceCost}*\n";
        }
        $message .= "Total : Rp. *{$grandTotal}*\n\n";


        $message .= "Jika Anda tidak merasa melakukan pembatalan, silakan hubungi layanan pelanggan kami.\n\n";
        $message .= "Terima kasih.";

        return $message;
    }

    /**
     * Get the array representation for the database channel (in-app).
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toDatabase($notifiable): array
    {
        $isOrderOwner = $notifiable->id === $this->order->user_id;
        $isAdmin = $notifiable->role === 'admin';

        // Different messages for different user types
        if ($isOrderOwner && !$isAdmin) {
            // Message for the customer
            $message = "Your order #{$this->order->id} has been cancelled.";
        } else {
            // Message for admin
            $orderOwnerName = $this->order->user->name ?? 'Unknown';
            $message = "Order #{$this->order->id} from {$orderOwnerName} has been cancelled.";
        }

        if ($this->reason) {
            $message .= " Reason: {$this->reason}";
        }

        return [
            'title' => 'Order Cancelled',
            'message' => $message,
            'type' => 'order',
            'action' => 'status_updated',
            'order' => [
                'id' => $this->order->id,
                'jubelio_order_id' => $this->order->jubelio_order_id,
                'old_status' => $this->order->status,
                'new_status' => Order::STATUS_CANCELLED,
                'grand_total' => $this->order->grand_total,
                'updated_at' => $this->order->updated_at->toISOString(),
            ],
            'updated_by' => null,
            'order_owner' => [
                'id' => $this->order->user_id,
                'name' => $this->order->user->name ?? 'Unknown',
            ],
        ];
    }

    /**
     * Get the array representation of the notification (default/fallback).
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable): array
    {
        // This can be kept simple for other channels or as a fallback
        return [
            'order_id' => $this->order->id,
            'message' => 'Order #' . $this->order->id . ' has been cancelled.',
            'url' => $this->frontend_base_url . '/transactions/' . $this->order->id,
        ];
    }
}
