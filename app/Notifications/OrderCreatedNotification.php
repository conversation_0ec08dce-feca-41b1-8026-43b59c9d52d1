<?php

namespace App\Notifications;

use App\Models\Order;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class OrderCreatedNotification extends Notification
{
    use Queueable;

    public $createdOrder;
    public $createdBy;

    /**
     * Create a new notification instance.
     */
    public function __construct(Order $createdOrder, User $createdBy)
    {
        $this->createdOrder = $createdOrder;
        $this->createdBy = $createdBy;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    // /**
    //  * Get the mail representation of the notification.
    //  */
    // public function toMail(object $notifiable): MailMessage
    // {
    //     return (new MailMessage)
    //         ->line('The introduction to the notification.')
    //         ->action('Notification Action', url('/'))
    //         ->line('Thank you for using our application!');
    // }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $isCreator = $notifiable->id === $this->createdBy->id;

        $title = $isCreator ? 'Your Order Has Been Created' : 'New Order Created';
        $message = $isCreator
            ? "Your order #{$this->createdOrder->id} has been created and is awaiting payment."
            : "A new order #{$this->createdOrder->id} ({$this->createdOrder->jubelio_order_id}) was created by {$this->createdBy->name}.";

        return [
            'title' => $title,
            'message' => $message,
            'type' => 'order',
            'action' => 'created',
            'order' => [
                'id' => $this->createdOrder->id,
                'jubelio_order_id' => $this->createdOrder->jubelio_order_id,
                'status' => $this->createdOrder->status,
                'grand_total' => $this->createdOrder->grand_total,
                'created_at' => $this->createdOrder->created_at->toISOString(),
            ],
            'created_by' => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->name,
                'role' => $this->createdBy->role,
            ],
        ];
    }
}
