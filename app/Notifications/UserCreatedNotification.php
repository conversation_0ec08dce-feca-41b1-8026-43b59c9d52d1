<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UserCreatedNotification extends Notification
{
    use Queueable;

    public $createdUser;
    public $createdBy;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $createdUser, User $createdBy)
    {
        $this->createdUser = $createdUser;
        $this->createdBy = $createdBy;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    // /**
    //  * Get the mail representation of the notification.
    //  */
    // public function toMail(object $notifiable): MailMessage
    // {
    //     return (new MailMessage)
    //         ->line('The introduction to the notification.')
    //         ->action('Notification Action', url('/'))
    //         ->line('Thank you for using our application!');
    // }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $isNewUser = $notifiable->id === $this->createdUser->id;

        if ($isNewUser) {
            // Message for the newly created admin
            $title = 'Welcome!';
            $message = 'An admin account has been created for you. For your security, please change your temporary password immediately.';
        } else {
            // Message for the other admins
            $title = 'New User Created';
            $message = "A new user '{$this->createdUser->name}' ({$this->createdUser->role}) has been created by {$this->createdBy->name}.";
        }

        return [
            'title' => $title,
            'message' => $message,
            'type' => 'user',
            'action' => 'created',
            'user' => [
                'id' => $this->createdUser->id,
                'name' => $this->createdUser->name,
                'email' => $this->createdUser->email,
                'role' => $this->createdUser->role,
                'created_at' => $this->createdUser->created_at->toISOString(),
            ],
            'created_by' => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->name,
            ],
        ];
    }
}
