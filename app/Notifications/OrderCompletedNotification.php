<?php

namespace App\Notifications;

use App\Channels\WahaChannel;
use App\Contracts\WahaNotification;
use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class OrderCompletedNotification extends BaseNotification implements ShouldQueue, WahaNotification
{
    use Queueable;

    protected $order;

    private $frontend_base_url;

    /**
     * Create a new notification instance.
     *
     * @param Order $order
     */
    public function __construct(Order $order)
    {
        if ($order->relationLoaded('items') && $order->relationLoaded('user')) {
            $this->order = $order;
        } else {
            $this->order = $order->load(['items', 'user']);
        }

        $this->frontend_base_url = config('frontend.otoresell_url');
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        $url = $this->frontend_base_url . '/transactions/' . $this->order->id;

        return (new MailMessage)
            ->subject('Pesanan Se<PERSON>ai - Order #' . $this->order->id)
            ->markdown('mail.order.completed', [
                'order' => $this->order,
                'url' => $url
            ]);
    }

    /**
     * Get the Waha representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return string
     */
    public function toWaha($notifiable): string
    {
        $userName = $this->order->user->name;
        $orderId = $this->order->id;
        $items = $this->order->items;
        $shippingFullName = $this->order->shipping_full_name;
        $shippingAddress = $this->order->shipping_address . ', ' . $this->order->shipping_subdistrict . ', ' . $this->order->shipping_district . ', ' . $this->order->shipping_city . ', ' . $this->order->shipping_province . ' ' . $this->order->shipping_post_code;
        $shippingPhone = $this->order->shipping_phone;
        $trackingNumber = $this->order->tracking_number;
        $expedition = $this->order->expedition;

        $message = "Assalaamu'alaikum {$userName} 🙂\n\n";
        $message .= "Order kamu dengan no: {$orderId} sudah selesai.\n\n";

        if ($trackingNumber && $expedition) {
            $message .= "Expedition: {$expedition['name']}\n";
            $message .= "Tracking Number: {$trackingNumber}\n\n";
        }

        $message .= "Berikut daftar produknya :\n\n";

        foreach ($items as $key => $item) {
            $itemNumber = $key + 1;
            $productName = "*{$item->name}*";
            $quantity = $item->quantity;
            $price = number_format($item->price, 0, ',', '.');
            $totalPrice = number_format($item->total_price, 0, ',', '.');
            $message .= "{$itemNumber}. {$productName} {$quantity} x Rp. {$price} = Rp. *{$totalPrice}*\n";
        }

        $subTotal = number_format($this->order->sub_total, 0, ',', '.');
        $shippingCost = number_format($this->order->shipping_cost, 0, ',', '.');
        $totalDiscount = number_format($this->order->total_discount, 0, ',', '.');
        $insuranceCost = number_format($this->order->insurance_cost, 0, ',', '.');
        $grandTotal = number_format($this->order->grand_total, 0, ',', '.');

        $message .= "\nSubtotal: Rp. *{$subTotal}*\n";
        $message .= "Ongkir: Rp. *{$shippingCost}*\n";
        if ($this->order->total_discount > 0) {
            $message .= "Total Diskon: -Rp. *{$totalDiscount}*\n";
        }
        if ($this->order->insurance_cost > 0) {
            $message .= "Asuransi: Rp. *{$insuranceCost}*\n";
        }
        $message .= "Total : Rp. *{$grandTotal}*\n\n";

        $message .= "Alamat pengiriman ke :\n";
        $message .= "*{$shippingFullName}*\n";
        $message .= "{$shippingAddress}\n";
        $message .= "No. HP: {$shippingPhone}\n\n";

        $message .= "Terima kasih 🙂";

        return $message;
    }

    /**
     * Get the array representation for the database channel (in-app).
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toDatabase($notifiable): array
    {
        $isOrderOwner = $notifiable->id === $this->order->user_id;
        $isAdmin = $notifiable->role === 'admin';

        // Different messages for different user types
        if ($isOrderOwner && !$isAdmin) {
            // Message for the customer
            $message = "Your order #{$this->order->id} has been completed and shipped.";
        } else {
            // Message for admin
            $orderOwnerName = $this->order->user->name ?? 'Unknown';
            $message = "Order #{$this->order->id} from {$orderOwnerName} has been completed.";
        }

        return [
            'title' => 'Order Completed',
            'message' => $message,
            'type' => 'order',
            'action' => 'completed',
            'order' => [
                'id' => $this->order->id,
                'jubelio_order_id' => $this->order->jubelio_order_id,
                'new_status' => Order::STATUS_COMPLETED,
                'grand_total' => $this->order->grand_total,
                'tracking_number' => $this->order->tracking_number,
                'expedition' => $this->order->expedition,
                'updated_at' => $this->order->updated_at->toISOString(),
            ],
            'updated_by' => null,
            'order_owner' => [
                'id' => $this->order->user_id,
                'name' => $this->order->user->name ?? 'Unknown',
            ],
        ];
    }

    /**
     * Get the array representation of the notification (default/fallback).
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable): array
    {
        // This can be kept simple for other channels or as a fallback
        return [
            'order_id' => $this->order->id,
            'message' => 'Order #' . $this->order->id . ' has been completed.',
            'url' => $this->frontend_base_url . '/transactions/' . $this->order->id,
        ];
    }
}
