<?php

namespace App\Notifications;

use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class OrderCompletedNotification extends BaseNotification implements ShouldQueue
{
    use Queueable;

    protected $order;

    private $frontend_base_url;

    /**
     * Create a new notification instance.
     *
     * @param Order $order
     */
    public function __construct(Order $order)
    {
        if ($order->relationLoaded('items') && $order->relationLoaded('user')) {
            $this->order = $order;
        } else {
            $this->order = $order->load(['items', 'user']);
        }

        $this->frontend_base_url = config('frontend.otoresell_url');
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        $url = $this->frontend_base_url . '/transactions/' . $this->order->id;

        return (new MailMessage)
            ->subject('<PERSON><PERSON><PERSON> - Order #' . $this->order->id)
            ->markdown('mail.order.completed', [
                'order' => $this->order,
                'url' => $url
            ]);
    }

    /**
     * Get the array representation for the database channel (in-app).
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toDatabase($notifiable): array
    {
        $isOrderOwner = $notifiable->id === $this->order->user_id;
        $isAdmin = $notifiable->role === 'admin';

        // Different messages for different user types
        if ($isOrderOwner && !$isAdmin) {
            // Message for the customer
            $message = "Your order #{$this->order->id} has been completed and shipped.";
        } else {
            // Message for admin
            $orderOwnerName = $this->order->user->name ?? 'Unknown';
            $message = "Order #{$this->order->id} from {$orderOwnerName} has been completed.";
        }

        return [
            'title' => 'Order Completed',
            'message' => $message,
            'type' => 'order',
            'action' => 'completed',
            'order' => [
                'id' => $this->order->id,
                'jubelio_order_id' => $this->order->jubelio_order_id,
                'new_status' => Order::STATUS_COMPLETED,
                'grand_total' => $this->order->grand_total,
                'tracking_number' => $this->order->tracking_number,
                'expedition' => $this->order->expedition,
                'updated_at' => $this->order->updated_at->toISOString(),
            ],
            'updated_by' => null,
            'order_owner' => [
                'id' => $this->order->user_id,
                'name' => $this->order->user->name ?? 'Unknown',
            ],
        ];
    }

    /**
     * Get the array representation of the notification (default/fallback).
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable): array
    {
        // This can be kept simple for other channels or as a fallback
        return [
            'order_id' => $this->order->id,
            'message' => 'Order #' . $this->order->id . ' has been completed.',
            'url' => $this->frontend_base_url . '/transactions/' . $this->order->id,
        ];
    }
}
