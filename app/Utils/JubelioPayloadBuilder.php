<?php

namespace App\Utils;

class JubelioPayloadBuilder
{
    /**
     * Build the payload for updating a Jubelio sales order with a tracking number.
     *
     * @param array $currentSalesOrder
     * @param string $trackingNumber
     * @return array
     */
    public static function buildJubelioUpdatePayload(array $currentSalesOrder, string $trackingNumber): array
    {
        $payload = [
            'salesorder_id' => $currentSalesOrder['salesorder_id'],
            'salesorder_no' => $currentSalesOrder['salesorder_no'],
            'contact_id' => $currentSalesOrder['contact_id'],
            'customer_name' => $currentSalesOrder['customer_name'],
            'transaction_date' => $currentSalesOrder['transaction_date'],
            'location_id' => $currentSalesOrder['location_id'],
            'source' => $currentSalesOrder['source'],
            'items' => [],
            'is_paid' => true,
        ];

        foreach ($currentSalesOrder['items'] as $item) {
            $payload['items'][] = [
                'salesorder_detail_id' => $item['salesorder_detail_id'],
                'item_id' => $item['item_id'],
                'tax_id' => $item['tax_id'],
                'price' => (float) $item['price'],
                'unit' => $item['unit'],
                'qty_in_base' => (float) $item['qty_in_base'],
                'disc' => (float) $item['disc'],
                'disc_amount' => (float) $item['disc_amount'],
                'tax_amount' => (float) $item['tax_amount'],
                'amount' => (float) $item['amount'],
                'location_id' => $item['loc_id'],
                'serial_no' => $item['serial_no'],
                'description' => $item['description'],
                'shipper' => $currentSalesOrder['shipper'],
                'channel_order_detail_id' => $item['channel_order_detail_id'],
                'tracking_no' => $trackingNumber,
            ];
        }

        return $payload;
    }
}
