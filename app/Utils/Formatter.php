<?php

namespace App\Utils;

class Formatter
{
    public static function normalizeCity(string $city = ''): string
    {
        return trim(preg_replace('/^(KAB\.|KOTA\s)/i', '', $city));
    }

    public static function normalizePhoneNumber(string $phone): string
    {
        return preg_replace('/[^0-9]/', '', $phone);
    }

    public static function normalizeRecipient(string $recipient): string
    {
        $recipient = self::normalizePhoneNumber($recipient);

        if (str_starts_with($recipient, '0')) {
            $recipient = '62' . substr($recipient, 1);
        }

        return $recipient;
    }
}
