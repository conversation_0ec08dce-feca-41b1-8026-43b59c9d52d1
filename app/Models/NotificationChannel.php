<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


/**
 * @mixin IdeHelperNotificationChannel
 */
class NotificationChannel extends Model
{
    use HasFactory;

    protected $fillable = [
        'slug',
        'name',
    ];

    public function userNotificationSettings() {
        return $this->hasMany(UserNotificationSetting::class);
    }
}
