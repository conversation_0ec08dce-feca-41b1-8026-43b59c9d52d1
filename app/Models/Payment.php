<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;


/**
 * @mixin IdeHelperPayment
 */
class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'external_id',
        'xendit_id',
        'status',
        'payment_method',
        'payment_channel',
        'payment_destination',
        'amount',
        'paid_amount',
        'currency',
        'invoice_url',
        'paid_at',
        'expires_at',
        'xendit_response',
        'webhook_payload',
        'failure_reason',
    ];

    /**
     * The attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'paid_amount' => 'decimal:2',
            'xendit_response' => 'array',
            'webhook_payload' => 'array',
        ];
    }

    protected function expiresAt(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ? Carbon::createFromFormat('Y-m-d H:i:s', $value, 'UTC') : null,
            set: fn ($value) => $value ? Carbon::parse($value)->setTimezone('UTC')->format('Y-m-d H:i:s') : null,
        );
    }

    protected function paidAt(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ? Carbon::createFromFormat('Y-m-d H:i:s', $value, 'UTC') : null,
            set: fn ($value) => $value ? Carbon::parse($value)->setTimezone('UTC')->format('Y-m-d H:i:s') : null,
        );
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }
}
