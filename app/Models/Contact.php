<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @mixin IdeHelperContact
 */
class Contact extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'phone',
        'email',
        'source',
        'jubelio_contact_id',
        'purpose',
        'source_data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'source_data' => 'array',
    ];

    /**
     * Get the orders for the contact.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    // === Constants for Purpose ===
    public const PURPOSE_RESELLER_DEFAULT = 'RESELLER_DEFAULT';

    /**
     * Get all possible purpose values.
     *
     * @return array<int, string>
     */
    public static function getPurposes(): array
    {
        return [
            self::PURPOSE_RESELLER_DEFAULT,
        ];
    }

    // === Constants for Cache Keys ===
    public const CACHE_KEY_RESELLER_DEFAULT_ID = 'jubelio_reseller_contact_id';
}
