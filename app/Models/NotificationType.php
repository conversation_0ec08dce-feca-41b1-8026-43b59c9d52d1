<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property UserNotificationSetting $userNotificationSetting
 * @mixin IdeHelperNotificationType
 */
class NotificationType extends Model
{
    use HasFactory;

    protected $fillable = [
        'slug',
        'name',
        'description',
    ];

    public function userNotificationSettings(): HasMany
    {
        return $this->hasMany(UserNotificationSetting::class);
    }
}
