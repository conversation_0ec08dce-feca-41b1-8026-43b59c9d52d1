<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


/**
 * @mixin IdeHelperOrderItem
 */
class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'jubelio_item_id',
        'jubelio_item_group_id',
        'sku',
        'name',
        'unit',
        'variant',
        'image',
        'weight', // on grams
        'original_price',
        'discount_percentage',
        'price',
        'quantity',
        'tax_id',
        'tax_rate_percent',
    ];

    protected $casts = [
        'variant' => 'array',
        'price' => 'decimal:2',
        'original_price' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'weight' => 'decimal:2',
        'tax_rate_percent' => 'decimal:2',
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function getTotalPriceAttribute(): float
    {
        return $this->price * $this->quantity;
    }

    public function getTaxAmountAttribute(): float
    {
        return ($this->total_price * $this->tax_rate_percent) / 100;
    }

    public function getTotalWithTaxAttribute(): float
    {
        return $this->total_price + $this->tax_amount;
    }
}
