<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperFixedPrice
 */
class FixedPrice extends Model
{
    use HasFactory;

    protected $fillable = [
        'jubelio_item_group_id',
        'jubelio_item_id',
        'name',
        'variant',
        'price',
    ];

    protected $casts = [
        'variant' => 'array',
        'price' => 'decimal:2',
    ];
}
