<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;


/**
 * @mixin IdeHelperSenderAddress
 */
class SenderAddress extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'sender_full_name',
        'sender_phone',
        'sender_address',
        'sender_province_id',
        'sender_province',
        'sender_city_id',
        'sender_city',
        'sender_district_id',
        'sender_district',
        'sender_subdistrict_id',
        'sender_subdistrict',
        'sender_post_code',
        'is_default',
    ];

    protected $casts = [
        'user_id' => 'integer',
        'is_default' => 'boolean',
    ];

    /**
     * Get the user that owns the sender address.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the orders associated with the sender address.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Scope a query to only include addresses for a specific user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to search addresses by various fields including user name.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('sender_full_name', 'like', "%{$search}%")
            ->orWhere('sender_phone', 'like', "%{$search}%")
            ->orWhere('sender_address', 'like', "%{$search}%")
            ->orWhere('sender_province', 'like', "%{$search}%")
            ->orWhere('sender_city', 'like', "%{$search}%")
            ->orWhere('sender_district', 'like', "%{$search}%")
            ->orWhere('sender_subdistrict', 'like', "%{$search}%")
            // Add search by user name (for admin)
            ->orWhereHas('user', function ($userQuery) use ($search) {
                $userQuery->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
            });
        });
    }

    /**
     * Scope a query to only include default addresses.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }
}
