<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;


/**
 * @mixin IdeHelperOrder
 */
class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'contact_id',
        'jubelio_contact_id',
        'jubelio_order_id',
        'user_id',
        'shipping_address_id',
        'user_name',
        'user_phone',
        'shipping_full_name',
        'shipping_phone',
        'shipping_address',
        'shipping_province_id',
        'shipping_province',
        'shipping_city_id',
        'shipping_city',
        'shipping_district_id',
        'shipping_district',
        'shipping_subdistrict_id',
        'shipping_subdistrict',
        'shipping_post_code',
        'status',
        'shipping_type',
        'tracking_number',
        'sub_total',
        'total_discount',
        'shipping_cost',
        'insurance_cost',
        'grand_total',
        'payment_method',
        'expedition',
        'etc',
    ];

    protected $casts = [
        'jubelio_contact_id' => 'integer',
        'jubelio_order_id' => 'integer',
        'user_id' => 'integer',
        'shipping_address_id' => 'integer',
        'grand_total' => 'decimal:2',
        'insurance_cost' => 'decimal:2',
        'total_discount' => 'decimal:2',
        'sub_total' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
        'expedition' => 'array',
        'etc' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function shippingAddress(): BelongsTo
    {
        return $this->belongsTo(ShippingAddress::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the contact for the order.
     */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class);
    }

    // Add helpful payment-related methods
    public function latestPayment(): HasOne
    {
        return $this->hasOne(Payment::class)->latestOfMany();
    }

    public function successfulPayments(): HasMany
    {
        return $this->hasMany(Payment::class)->where('status', 'paid');
    }

    public function getTotalPaidAmount(): float
    {
        return $this->successfulPayments()->sum('paid_amount');
    }

    public function isFullyPaid(): bool
    {
        return $this->getTotalPaidAmount() >= $this->grand_total;
    }

    public function getPaymentStatus(): string
    {
        $paidAmount = $this->getTotalPaidAmount();

        if ($paidAmount == 0) return 'unpaid';
        if ($paidAmount >= $this->grand_total) return 'paid';
        return 'partial';
    }

    // Your existing constants and methods...

    public const SHIPPING_TYPE_EXPEDITION = 'expedition';
    public const SHIPPING_TYPE_ONLINE_RECIPIENT = 'online_recipient';
    public const SHIPPING_TYPE_STORE_PICKUP = 'store_pickup';
    public const SHIPPING_TYPE_JNE_EXPEDITION = 'jne_expedition';

    public static function getShippingTypes(): array
    {
        return [
            self::SHIPPING_TYPE_EXPEDITION,
            self::SHIPPING_TYPE_ONLINE_RECIPIENT,
            self::SHIPPING_TYPE_STORE_PICKUP,
            self::SHIPPING_TYPE_JNE_EXPEDITION,
        ];
    }

    public const STATUS_PENDING = 'pending';
    public const STATUS_PAID = 'paid';
    public const STATUS_CONFIRMED = 'confirmed';
    public const STATUS_PROCESSING = 'processing';
    public const STATUS_SHIPPED = 'shipped';
    public const STATUS_DELIVERED = 'delivered';
    public const STATUS_COMPLETED = 'completed';
    public const STATUS_CANCELLED = 'cancelled';
    public const STATUS_REFUNDED = 'refunded';

    public static function getStatuses(): array
    {
        return [
            self::STATUS_PENDING,
            self::STATUS_PAID,
            self::STATUS_CONFIRMED,
            self::STATUS_PROCESSING,
            self::STATUS_SHIPPED,
            self::STATUS_DELIVERED,
            self::STATUS_COMPLETED,
            self::STATUS_CANCELLED,
            self::STATUS_REFUNDED,
        ];
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByContact($query, int $contactId)
    {
        return $query->where('contact_id', $contactId);
    }

    public function scopeSearch($query, string $search, string $userRole)
    {
        return $query->where(function ($q) use ($search, $userRole) {
            $q->where('jubelio_order_id', 'like', "%{$search}%")
                ->orWhere('status', 'like', "%{$search}%")
                ->orWhere('payment_method', 'like', "%{$search}%")
                ->orWhere('shipping_full_name', 'like', "%{$search}%")
                ->orWhere('shipping_phone', 'like', "%{$search}%");

            if ($userRole === User::ROLE_ADMIN) {
                $q->orWhere('user_name', 'like', "%{$search}%")
                  ->orWhere('user_phone', 'like', "%{$search}%");
            }
        });
    }

    /**
     * Recalculate sub_total and total_discount based on its items.
     */
    public function recalculateTotals(): void
    {
        $sub_total = 0;
        $total_discount = 0;

        foreach ($this->items as $item) {
            $sub_total += (float) $item->quantity * (float) $item->original_price;
            $total_discount += (float) $item->quantity * ((float) $item->original_price - (float) $item->price);
        }

        $this->sub_total = $sub_total;
        $this->total_discount = $total_discount;
        $this->grand_total = $this->sub_total - $this->total_discount + $this->shipping_cost + $this->insurance_cost;
    }
}
