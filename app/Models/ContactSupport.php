<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


/**
 * @mixin IdeHelperContactSupport
 */
class ContactSupport extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'category',
        'type',
        'value',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Define constants for the support categories.
     * This makes the code cleaner and avoids magic strings.
     */
    public const CATEGORY_ACTIVATION = 'activation';
    public const CATEGORY_TECHNICAL = 'technical_support';
    public const CATEGORY_BILLING = 'billing';
    public const CATEGORY_GENERAL = 'general_inquiry';

    /**
     * Define constants for the contact types.
     */
    public const TYPE_WHATSAPP = 'whatsapp';
    public const TYPE_EMAIL = 'email';
    public const TYPE_PHONE = 'phone';
    public const TYPE_TELEGRAM = 'telegram';


    /**
     * Get a list of all available categories.
     * Useful for validation rules and frontend dropdowns.
     *
     * @return array<string>
     */
    public static function getCategories(): array
    {
        return [
            self::CATEGORY_ACTIVATION,
            self::CATEGORY_TECHNICAL,
            self::CATEGORY_BILLING,
            self::CATEGORY_GENERAL,
        ];
    }

    /**
     * Get a list of all available types.
     * Useful for validation rules and frontend dropdowns.
     *
     * @return array<string>
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_WHATSAPP,
            self::TYPE_EMAIL,
            self::TYPE_PHONE,
            self::TYPE_TELEGRAM,
        ];
    }
}
