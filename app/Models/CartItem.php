<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperCartItem
 */
class CartItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'jubelio_item_id',
        'jubelio_item_group_id',
        'sku',
        'name',
        'unit',
        'price',
        'quantity',
        'weight',
        'variant',
        'image',
        'tax_id',
        'tax_rate_percent',
    ];

    protected $casts = [
        'variant' => 'array',
        'price' => 'decimal:2',
        'weight' => 'decimal:2',
        'tax_rate_percent' => 'decimal:2',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getTotalPriceAttribute(): float
    {
        return $this->price * $this->quantity;
    }

    public function getTaxAmountAttribute(): float
    {
        return ($this->total_price * $this->tax_rate_percent) / 100;
    }

    public function getTotalWithTaxAttribute(): float
    {
        return $this->total_price + $this->tax_amount;
    }
}
