<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


/**
 * @mixin IdeHelperFrontendApp
 */
class FrontendApp extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'identifier',
        'auto_assign_to_role',
        'maintenance_status',
        'force_logout_before',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'maintenance_status' => 'boolean',
        'force_logout_before' => 'datetime',
    ];

    /**
     * The users that belong to the frontend app.
     */
    public function users()
    {
        return $this->belongsToMany(User::class);
    }
}
