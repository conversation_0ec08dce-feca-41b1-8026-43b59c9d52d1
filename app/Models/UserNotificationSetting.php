<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


/**
 * @mixin IdeHelperUserNotificationSetting
 */
class UserNotificationSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'notification_type_id',
        'notification_channel_id',
    ];

    public function user() {
        return $this->belongsTo(User::class);
    }

    public function type() {
        return $this->belongsTo(NotificationType::class, 'notification_type_id');
    }

    public function notificationChannel()
    {
        return $this->belongsTo(NotificationChannel::class, 'notification_channel_id');
    }
}
