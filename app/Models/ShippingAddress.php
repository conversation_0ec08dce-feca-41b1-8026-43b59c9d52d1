<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;


/**
 * @mixin IdeHelperShippingAddress
 */
class ShippingAddress extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'shipping_full_name',
        'shipping_phone',
        'shipping_address',
        'shipping_province_id',
        'shipping_province',
        'shipping_city_id',
        'shipping_city',
        'shipping_district_id',
        'shipping_district',
        'shipping_subdistrict_id',
        'shipping_subdistrict',
        'shipping_post_code',
    ];

    protected $casts = [
        'user_id' => 'integer',
    ];

    /**
     * Get the user that owns the shipping address.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function orders(): HasMany
    {
    return $this->hasMany(Order::class);
    }

    /**
     * Scope a query to only include addresses for a specific user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to search addresses by various fields including user name.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('shipping_full_name', 'like', "%{$search}%")
            ->orWhere('shipping_phone', 'like', "%{$search}%")
            ->orWhere('shipping_address', 'like', "%{$search}%")
            ->orWhere('shipping_province', 'like', "%{$search}%")
            ->orWhere('shipping_city', 'like', "%{$search}%")
            ->orWhere('shipping_district', 'like', "%{$search}%")
            ->orWhere('shipping_subdistrict', 'like', "%{$search}%")
            // Add search by user name (for admin)
            ->orWhereHas('user', function ($userQuery) use ($search) {
                $userQuery->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
            });
        });
    }
}
