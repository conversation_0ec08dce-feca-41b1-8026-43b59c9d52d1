APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=otoapi
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=mailgun
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Zafrada Kediri"
# MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Jubelio Data
JUBELIO_BASE_URL=https://api2.jubelio.com
JUBELIO_EMAIL=
JUBELIO_PASSWORD=
JUBELIO_SECRET_KEY=

XENDIT_SECRET_KEY=your_xendit_secret_key
XENDIT_PUBLIC_KEY=your_xendit_public_key
XENDIT_WEBHOOK_TOKEN=your_webhook_verification_token
XENDIT_BASE_URL=https://api.xendit.co


OTORESELL_URL=http://localhost:3000

MAILGUN_DOMAIN=zafrada.com
MAILGUN_SECRET=your_mailgun_secret_key
MAILGUN_ENDPOINT=api.mailgun.net

JNE_BASE_URL=https://apiv2.jne.co.id:10202/tracing/api/generatecnote
JNE_USERNAME=
JNE_API_KEY=
JNE_SHIPPER_NAME="Zafrada Store"
JNE_SHIPPER_ADDRESS="Jalan Masjid No 35 Karangrejo Ngasem"
JNE_SHIPPER_CITY="Kab Kediri"
JNE_SHIPPER_ZIP=64182
JNE_SHIPPER_PHONE=082143724860
JNE_ORIGIN_CODE=KDR10000
JNE_BRANCH_CODE=KDR000
