<?php

namespace Database\Seeders;

use App\Models\MembershipLevel;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MembershipLevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        MembershipLevel::updateOrCreate(
            ['name' => 'Silver'],
            [
                'description' => 'Standard level for all new resellers.',
                'discount_percentage' => 5.00,
                'is_default' => true, // This is the default level
            ]
        );

        MembershipLevel::updateOrCreate(
            ['name' => 'Gold'],
            [
                'description' => 'For resellers with consistent performance.',
                'discount_percentage' => 10.00,
                'is_default' => false,
            ]
        );

        MembershipLevel::updateOrCreate(
            ['name' => 'Diamond'],
            [
                'description' => 'For top-performing resellers.',
                'discount_percentage' => 20.00,
                'is_default' => false,
            ]
        );
    }
}
