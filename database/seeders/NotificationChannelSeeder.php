<?php

namespace Database\Seeders;

use App\Models\NotificationChannel;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NotificationChannelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        NotificationChannel::updateOrCreate(['slug' => 'mail'], ['name' => 'Email']);
        NotificationChannel::updateOrCreate(['slug' => 'whatsapp'], ['name' => 'WhatsApp']);
        NotificationChannel::updateOrCreate(['slug' => 'off'], ['name' => 'Off']);
    }
}
