<?php

namespace Database\Seeders;

use App\Models\FrontendApp;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FrontendAppSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        FrontendApp::updateOrCreate(
            ['identifier' => 'otoadmin-web'],
            [
                'name' => 'Otoadmin',
                'auto_assign_to_role' => User::ROLE_ADMIN, // Assign to admin role
            ]

        );

        FrontendApp::updateOrCreate(
            ['identifier' => 'otoresell-web'],
            [
                'name' => 'Otoresell',
                'auto_assign_to_role' => User::ROLE_RESELLER, // Assign to reseller role
            ]
        );
    }
}
