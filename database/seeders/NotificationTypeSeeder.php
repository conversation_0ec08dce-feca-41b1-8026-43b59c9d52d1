<?php

namespace Database\Seeders;

use App\Models\NotificationType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NotificationTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        NotificationType::updateOrCreate(
            ['slug' => 'order_paid'],
            ['name' => 'Order Paid', 'description' => 'When an order is successfully paid.']
        );
        NotificationType::updateOrCreate(
            ['slug' => 'order_completed'],
            ['name' => 'Order Completed', 'description' => 'When an order is completed.']
        );
        NotificationType::updateOrCreate(
            ['slug' => 'order_cancelled'],
            ['name' => 'Order Cancelled', 'description' => 'When an order is cancelled.']
        );
    }
}
