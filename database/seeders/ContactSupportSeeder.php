<?php

namespace Database\Seeders;

use App\Models\ContactSupport;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ContactSupportSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        ContactSupport::updateOrCreate(
            ['name' => 'CS Zafrada'],
            [
                'category' => ContactSupport::CATEGORY_ACTIVATION,
                'type' => ContactSupport::TYPE_WHATSAPP,
                'value' => '6282143724860',
                'is_active' => true,
            ]
        );
    }
}
