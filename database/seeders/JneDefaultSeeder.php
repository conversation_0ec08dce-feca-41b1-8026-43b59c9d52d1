<?php

namespace Database\Seeders;

use App\Models\JneBranch;
use App\Models\JneOrigin;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class JneDefaultSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            // Set all branches to not be default
            JneBranch::query()->update(['is_default' => false]);

            // Set the specified branch to be default
            JneBranch::where('branch_code', 'KDR000')->update(['is_default' => true]);

            // Set all origins to not be default
            JneOrigin::query()->update(['is_default' => false]);

            // Set the specified origin to be default
            JneOrigin::where('origin_code', 'KDR10000')->update(['is_default' => true]);
        });
    }
}
