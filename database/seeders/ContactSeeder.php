<?php

namespace Database\Seeders;

use App\Models\Contact;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ContactSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            // Create a contact with the RESELLER_DEFAULT purpose
            Contact::updateOrCreate(
                ['purpose' => Contact::PURPOSE_RESELLER_DEFAULT],
                [
                    'name' => 'Oresell',
                    'email' => '<EMAIL>',
                    'phone' => '081232980483',
                    'source' => 'jubelio',
                    'jubelio_contact_id' => 1737,
                    'source_data' => ['info' => 'This is the default contact for resellers created by the seeder.'],
                ]
            );
        });
    }
}
