JNT API endpoint:



1. Create AWB:
Request Method	:	POST
Header	:	application/x-www-form-urlencoded
URL	:	/jnt-awb
Body 	:
- data_param
- data_sign -> base64_encode(md5(($data_param).$key));

payload example:
$data = array
(
   'username'=>'username',
   'api_key'=>'api_key',
   'orderid'=>'ORDERID-0001'
   'shipper_name'=>'PENGIRIM',
   'shipper_contact'=>'PENGIRIM',
   'shipper_phone'=> '+628123456789',
   'shipper_addr'=>'JL. Pengirim no.88, RT/RW:001/010, Pluit',
   'origin_code'=>'JKT',
   'receiver_name'=>'PENERIMA',
   'receiver_phone'=>'+62812348888',
   'receiver_addr'=>'JL. Penerima no.1, RT/RW:04/07, Sidoarjo',
   'receiver_zip'=>'40123',
   'destination_code'=>'JKT',
   'receiver_area'=>'JKT001',
   'qty'=>'1',
   'weight'=>'1',
   'goodsdesc'=>'TESTING!!',
   'servicetype'=>'1',
   'insurance'=>'122',
   'orderdate'=>'2021-08-01 22:02:00',
   'item_name'=>'topi',
   'cod'=>'120000',
   'sendstarttime'=>'2021-08-01 08:00:00',
   'sendendtime'=>'2021-08-01 22:00:00',
   'expresstype'=>'1',
   'goodsvalue'=>'1000',
);
data_json = json_encode(array('detail'=>array($data)));
$data_request = array
(
   'data_param'=>$data_json,
   'data_sign'=> base64_encode(md5($data_json.$key))
);

success response example:
{
    "success": true,
    "desc": "Request berhasil",
    "detail": [
        {
            "awb_no": "JO0027364832",
            "orderid": "ORDERID-0001",
            "desCode": "JKT-JKT001",
            "etd": "2-4",
            "status": "Sukses",
        }
    ]
}

failed response example:
{
    "success": true,
    "desc": "Request berhasil",
    "detail": [
        {
            "orderid": "ORDERID-0001",
            "desCode": "JKT-JKT001",
            "etd": "No Data",
            "status": "Error",
            "reason": "Orderid tidak boleh sama"
        }
    ]
}


2. Tracking:
Request Method	:	POST
Authorization	:	Basic Authorization
URL	:	/jnt-tracking
Body	:	Raw (Text)
- awb
- eccompanyid

payload example:
$billcode ='JD1234567890';
$data = array
(
    'awb'=>$billcode,
    'eccompanyid'=>'.......'
);
$jsonData = json_encode($data);


3. Tariff Check
Request Method	:	POST
Header	:	application/x-www-form-urlencoded
URL	:	/jnt-tariff
Body	:	x-www-form-urlencoded
- data
- sign -> base64_encode(md5(($data_param).$key))

Example payload:
$data = array
(
                'weight'=>"1"
                ,'sendSiteCode'=>'JAKARTA'
                ,'destAreaCode'=>'KALIDERES'
                ,'cusName'=>'........'
                ,'productType'=>'EZ'
);
$jason=json_encode($data);
$mmm = base64_encode(md5($jason.$key));

$data1 = array
(
    'data'=>$jason
    ,'sign'=>$mmm
);

example success response:
{
   "is_success":"true",
   "message":"",
   "content":"[{\"name\":\"EZ\",\"cost\":\"9000\"}]"
}

example filed response:
{
   "is_success":"false",
   "message":"invalid Authorization"
}

4.Cancel AWB
Request Method	:	POST
Header	:	application/x-www-form-urlencoded
URL	:	/jnt-awb-cancel
Body	:	x-www-form-urlencoded
- data_param
- data_sign -> base64_encode(md5(($data_param).$key))

example payload:
$data = array
(
    'username'=>'....'
    ,'api_key'=>'......'
    ,'orderid'=>'ORDERID-12345678'
    ,'remark'=>'Canceled by E-Commerce'
);
$data_param    = json_encode(array('detail'=>array($data)));
$data_sign     = base64_encode(md5(($data_param).$key));
$body_params = array (
    'data_params' => $data_params
    ,'data_sign' => $data_sign
);

example success response:
{
    "success": true,
    "desc": "Request berhasil",
    "detail": [
        {
            "awb_no": "JO0027364832",
            "orderid": "ORDERID-0001",
            "desCode": "JKT-JKT001",
            "etd": "2-4",
            "status": "Sukses",
        }
    ]
}

example failed response:
{
    "success": true,
    "desc": "Request berhasil",
    "detail": [
        {
            "orderid": "ORDERID-0001",
            "desCode": "JKT-JKT001",
            "etd": "No Data",
            "status": "Error",
            "reason": "Orderid tidak boleh sama"
        }
    ]
}
