Exapedition params structure:

{
    "name": "J&T",
    "code": "EZ",
    "service": "J&T EZ",
    "description": "J&T EZ",
    "cost": 60000,
    "etd": "1-6",
    "courier_id": 12,
    "courier_service_id": 1211
}

this 2 params bellow only provided in shiiping type :jubelio_shipment
"courier_id": 12,
"courier_service_id": 1211


etc structure:
{
  "jubelio_shipment": {
    "courier_id": 12,
    "shipment_id": 287,
    "awb": "JO0316070951",
    "tracking_url": "https://shipment.jubelio.com/s/3VKYvA8N7cgAPat3t5mpsM",
    "short_tracking_url": "https://jube.co/sh/y4mENd",
    "price": 10000,
    "extra_info": {
      "zone_id": "550-JBG07-04"
    }
  }
}
