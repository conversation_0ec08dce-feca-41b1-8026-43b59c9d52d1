<?php

namespace Database\Seeders;

use App\Models\FrontendApp;
use App\Models\MembershipLevel;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $adminApp = FrontendApp::where('identifier', 'otoadmin-web')->first();
            $resellerApp = FrontendApp::where('identifier', 'otoresell-web')->first();

            // 2. Find the default membership level you created in MembershipLevelSeeder
            $defaultResellerLevel = MembershipLevel::where('is_default', true)->first();

            // --- Admin User ---
            // Admin likely doesn't need a membership level, so we can leave it as null.
            $admin = User::updateOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'Fendy',
                    'password' => 'otoadmin',
                    'role' => 'admin',
                    'is_active' => true,
                    'phone' => '081232980483',
                    'email_verified_at' => Carbon::now()
                ]
            );

            if ($adminApp) {
                $admin->frontendApps()->syncWithoutDetaching([$adminApp->id]);
            }

            $admin2 = User::updateOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'Zuhri',
                    'password' => 'MujahidMuda25',
                    'role' => 'admin',
                    'is_active' => true,
                    'phone' => '085336383975',
                    'email_verified_at' => Carbon::now()
                ]
            );

            if ($adminApp) {
                $admin2->frontendApps()->syncWithoutDetaching([$adminApp->id]);
            }

            // --- Reseller User ---
            $resellerData = [
                'name' => 'Syaifudin Zuhri',
                'password' => 'TestingOtoresell',
                'role' => 'reseller',
                'is_active' => true,
                'phone' => '085336383975',
                'email_verified_at' => Carbon::now()
            ];

            // 3. Add the membership_level_id to the reseller's data if a default level exists
            if ($defaultResellerLevel) {
                $resellerData['membership_level_id'] = $defaultResellerLevel->id;
            }

            $reseller = User::updateOrCreate(
                ['email' => '<EMAIL>'],
                $resellerData
            );

            if ($resellerApp) {
                $reseller->frontendApps()->syncWithoutDetaching([$resellerApp->id]);
            }
        });
    }
}
