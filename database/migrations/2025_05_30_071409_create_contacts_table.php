<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contacts', function (Blueprint $table) {
            $table->id();

            // === Core Contact Details ===
            $table->string('name')->comment('A descriptive name for this contact');
            $table->string('phone')->nullable();
            $table->string('email')->nullable()->index();

            // === Integration & Business Logic ===
            $table->string('source')->default('jubelio')->index()->comment('The origin of the contact, e.g., jubelio, manual');
            $table->unsignedBigInteger('jubelio_contact_id')->unique()->nullable()->comment('The unique ID from the Jubelio system');
            $table->string('purpose')->nullable()->index()->comment('Defines the special role of this contact, e.g., RESELLER_DEFAULT');

            // The JSON column to store the full, original data from the source.
            $table->json('source_data')->nullable()->comment('The raw, complete JSON payload from the source system');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contacts');
    }
};
