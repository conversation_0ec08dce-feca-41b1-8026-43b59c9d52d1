<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();

            // It's highly likely the order ID from Jubelio should be unique.
            // Adding a unique index is better than a simple index.
            $table->unsignedBigInteger('jubelio_order_id')->unique();

            $table->unsignedBigInteger('jubelio_contact_id');
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('contact_id')->nullable()->constrained('contacts')->nullOnDelete();
            $table->foreignId('shipping_address_id')->nullable()->constrained()->nullOnDelete();

            // Denormalized user and shipping data (this is fine for order history)
            $table->string('user_name');
            $table->string('user_phone');
            $table->string('shipping_full_name');
            $table->string('shipping_phone');
            $table->text('shipping_address');
            $table->string('shipping_province_id');
            $table->string('shipping_province');
            $table->string('shipping_city_id');
            $table->string('shipping_city');
            $table->string('shipping_district_id');
            $table->string('shipping_district');
            $table->string('shipping_subdistrict_id');
            $table->string('shipping_subdistrict');
            $table->string('shipping_post_code')->nullable();

            $table->string('status')->default('pending');
            $table->string('shipping_type')->default('expedition')->comment('Can be expedition, online_recipient etc');
            $table->string('tracking_number')->nullable();

            // Financials
            $table->decimal('sub_total', 15, 2)->default(0);
            $table->decimal('total_discount', 15, 2)->default(0);
            $table->decimal('insurance_cost', 15, 2)->default(0);
            $table->decimal('shipping_cost', 15, 2)->default(0);
            $table->decimal('grand_total', 15, 2)->default(0);

            $table->string('payment_method')->nullable();
            $table->json('expedition')->nullable();
            $table->json('etc')->nullable();
            $table->timestamps();

            // === RECOMMENDED INDEXES ===

            // Foreign key indexes are already created by ->constrained()

            // Index for filtering by contact
            $table->index('jubelio_contact_id');

            // Index for filtering by status
            $table->index('status');

            // Index for filtering by shipping_type
            $table->index('shipping_type');

            // Index for searching by tracking number
            $table->index('tracking_number');

            // Index for sorting by date (very common)
            $table->index('created_at');

            // Composite index for common admin filter: Status + Date
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
