<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sender_addresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('sender_full_name');
            $table->string('sender_phone');
            $table->text('sender_address');
            $table->string('sender_province_id');
            $table->string('sender_province');
            $table->string('sender_city_id');
            $table->string('sender_city');
            $table->string('sender_district_id');
            $table->string('sender_district');
            $table->string('sender_subdistrict_id');
            $table->string('sender_subdistrict');
            $table->string('sender_post_code')->nullable();
            $table->boolean('is_default')->default(false);
            $table->timestamps();

            $table->index('sender_province_id');
            $table->index('sender_city_id');
            $table->index('sender_full_name');
            $table->index('sender_phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sender_addresses');
    }
};
