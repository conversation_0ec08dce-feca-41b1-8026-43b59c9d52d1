<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            // Core Identifiers
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->string('external_id')->unique()->comment('Our internal unique ID for the payment attempt');
            $table->string('xendit_id')->nullable()->index()->comment('The unique ID from the payment gateway');

            // Key Payment Details
            $table->string('status', 50)->default('pending')->comment('e.g., pending, paid, expired, failed');
            $table->string('payment_method', 50)->comment('The general method, e.g., BANK_TRANSFER, QR_CODE');
            $table->string('payment_channel', 100)->nullable()->comment('The specific channel used, e.g., MANDIRI, QRIS, DANA');
            $table->string('payment_destination')->nullable()->index()->comment('The VA number. Indexed for customer support lookups.');

            // Financial Information
            $table->decimal('amount', 15, 2)->comment('The amount to be paid');
            $table->decimal('paid_amount', 15, 2)->nullable()->comment('The actual amount paid by the customer');
            $table->string('currency', 10)->default('IDR');

            // URLs and Timestamps
            $table->text('invoice_url')->nullable()->comment('The URL the customer uses to pay');
            $table->timestamp('paid_at')->nullable()->comment('Timestamp when the payment was confirmed as paid');
            $table->timestamp('expires_at')->nullable()->comment('Timestamp when the payment invoice expires');

            // Flexible Data Storage (JSON)
            $table->json('xendit_response')->nullable()->comment('Full raw JSON response from Xendit on invoice creation');
            $table->json('webhook_payload')->nullable()->comment('Full raw JSON payload from the payment confirmation webhook');

            // Error Handling
            $table->string('failure_reason')->nullable();

            $table->timestamps(); // This creates created_at and updated_at

            // === COMPOSITE INDEXES for High-Performance Queries ===

            // For filtering payments list in an admin panel.
            // The order of columns matters: status is more selective.
            $table->index(['status', 'payment_method']);

            // For the cron job that finds and cancels expired payments.
            $table->index(['status', 'expires_at']);

            // For filtering payments by date range.
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
