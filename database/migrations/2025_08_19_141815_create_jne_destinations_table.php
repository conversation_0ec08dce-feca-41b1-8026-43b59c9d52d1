<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jne_destinations', function (Blueprint $table) {
            $table->id();
            $table->string('country_name');
            $table->string('province_name');
            $table->string('city_name');
            $table->string('district_name');
            $table->string('subdistrict_name');
            $table->string('zip_code', 10);
            $table->string('tariff_code');
            $table->timestamps();

            $table->unique(['country_name', 'province_name', 'city_name', 'district_name', 'subdistrict_name', 'zip_code'], 'jne_destinations_unique');
            $table->index('zip_code');
            $table->index('tariff_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jne_destinations');
    }
};
