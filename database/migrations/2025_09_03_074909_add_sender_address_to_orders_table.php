<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Group foreign key with other foreign keys for better readability
            $table->foreignId('sender_address_id')
                  ->nullable()
                  ->after('shipping_address_id') // Positioned with other FKs
                  ->constrained('sender_addresses')
                  ->nullOnDelete();

            // --- Sender Snapshot Columns (ALL NULLABLE) ---
            // Positioned after the new foreign key
            $table->string('sender_full_name')->nullable()->after('sender_address_id');
            $table->string('sender_phone')->nullable()->after('sender_full_name');
            $table->text('sender_address')->nullable()->after('sender_phone');
            $table->string('sender_province_id')->nullable()->after('sender_address');
            $table->string('sender_province')->nullable()->after('sender_province_id');
            $table->string('sender_city_id')->nullable()->after('sender_province');
            $table->string('sender_city')->nullable()->after('sender_city_id');
            $table->string('sender_district_id')->nullable()->after('sender_city');
            $table->string('sender_district')->nullable()->after('sender_district_id');
            $table->string('sender_subdistrict_id')->nullable()->after('sender_district');
            $table->string('sender_subdistrict')->nullable()->after('sender_subdistrict_id');
            $table->string('sender_post_code')->nullable()->after('sender_subdistrict');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropForeign(['sender_address_id']);
            $table->dropColumn([
                'sender_full_name',
                'sender_phone',
                'sender_address',
                'sender_province_id',
                'sender_province',
                'sender_city_id',
                'sender_city',
                'sender_district_id',
                'sender_district',
                'sender_subdistrict_id',
                'sender_subdistrict',
                'sender_post_code',
                'sender_address_id',
            ]);
        });
    }
};
