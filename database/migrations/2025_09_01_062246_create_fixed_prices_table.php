<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fixed_prices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('jubelio_item_group_id');
            $table->unsignedBigInteger('jubelio_item_id')->unique();

            // For easier management and consistency
            $table->string('name');
            $table->json('variant')->nullable(); // Use JSON to match order_items

            $table->decimal('price', 15, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fixed_prices');
    }
};
