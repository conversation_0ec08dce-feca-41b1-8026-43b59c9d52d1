<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_supports', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('category');
            $table->string('type');
            $table->string('value');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // --- OPTIMIZED COMPOSITE INDEXES ---

            // 1. For querying by active status and category.
            // This covers: WHERE is_active = ? AND category = ?
            // It also implicitly covers the simpler query: WHERE is_active = ?
            $table->index(['is_active', 'category']);

            // 2. For querying by active status and type.
            // This covers: WHERE is_active = ? AND type = ?
            // This is the index that directly addresses your latest feedback.
            $table->index(['is_active', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_supports');
    }
};
