<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cart_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // --- Core Product Identifiers ---
            // The specific variant being added to the cart.
            $table->unsignedBigInteger('jubelio_item_id');
            // The parent group ID, critical for linking to the discount.
            $table->unsignedBigInteger('jubelio_item_group_id')->index();

            // --- Denormalized Product Details ---
            // Storing these here makes cart retrieval fast without API calls.
            $table->string('sku');
            $table->string('name');
            $table->string('unit');
            $table->decimal('price', 15, 2);
            $table->integer('quantity');
            $table->decimal('weight', 8, 2); // in grams
            $table->json('variant')->nullable();
            $table->string('image')->nullable();

            // --- Financials ---
            $table->unsignedBigInteger('tax_id')->nullable();
            $table->decimal('tax_rate_percent', 5, 2)->default(0);

            $table->timestamps();

            // --- Indexes and Constraints ---
            // Enforce one row per item variant for a user.
            $table->unique(['user_id', 'jubelio_item_id']);
            // Index for efficient cleanup of old, abandoned carts.
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cart_items');
    }
};
