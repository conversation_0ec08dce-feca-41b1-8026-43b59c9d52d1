<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');

            // --- Core Product Identifiers (Snapshot at time of order) ---
            $table->unsignedBigInteger('jubelio_item_id');
            // The parent group ID, saved for historical discount accuracy and reporting.
            $table->unsignedBigInteger('jubelio_item_group_id')->index();

            // --- Denormalized Product Details ---
            $table->string('sku');
            $table->string('name');
            $table->string('unit');
            $table->json('variant')->nullable();
            $table->string('image')->nullable();
            $table->decimal('weight', 8, 2); // in grams

            // --- Financial Snapshot ---
            // The price of the item BEFORE any discounts were applied.
            $table->decimal('original_price', 15, 2);
            // The effective discount percentage applied to this item.
            $table->decimal('discount_percentage', 5, 2)->default(0.00);
            // The final price per unit after the discount (original_price - discount).
            $table->decimal('price', 15, 2);
            $table->integer('quantity');

            // --- Tax Snapshot ---
            $table->unsignedBigInteger('tax_id')->nullable();
            $table->decimal('tax_rate_percent', 5, 2)->default(0);

            $table->timestamps();

            // --- Indexes and Constraints ---
            // Enforce that an item can only appear once per order.
            $table->unique(['order_id', 'jubelio_item_id']);
            // Critical for sales reporting and analytics (e.g., "how many of item X did we sell?").
            $table->index('jubelio_item_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
