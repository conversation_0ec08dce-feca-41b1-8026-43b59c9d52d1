<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_addresses', function (Blueprint $table) {
            $table->id();

            // This is the most important column for filtering.
            // The ->constrained() method automatically creates a foreign key AND an index on user_id.
            // No extra index is needed for this column.
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            $table->string('shipping_full_name');
            $table->string('shipping_phone');
            $table->text('shipping_address');
            $table->string('shipping_province_id');
            $table->string('shipping_province');
            $table->string('shipping_city_id');
            $table->string('shipping_city');
            $table->string('shipping_district_id');
            $table->string('shipping_district');
            $table->string('shipping_subdistrict_id');
            $table->string('shipping_subdistrict');
            $table->string('shipping_post_code')->nullable();
            $table->timestamps();

            // === RECOMMENDED ADDITIONAL INDEXES ===

            // For allowing admins to efficiently filter addresses by location.
            // We index the ID columns as they are more efficient for lookups than string names.
            $table->index('shipping_province_id');
            $table->index('shipping_city_id');

            // For allowing users or admins to search for an address by name or phone.
            $table->index('shipping_full_name');
            $table->index('shipping_phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_addresses');
    }
};
