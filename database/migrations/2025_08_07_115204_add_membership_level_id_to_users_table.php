<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('membership_level_id')->nullable()->after('role')->constrained('membership_levels')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // The most convenient way, as per Laravel 12 docs.
            // Laravel will resolve 'users_membership_level_id_foreign' automatically.
            $table->dropForeign(['membership_level_id']);

            $table->dropColumn('membership_level_id');
        });
    }
};
