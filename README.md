<p align="center"><a href="https://laravel.com" target="_blank"><img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400" alt="Laravel Logo"></a></p>

<p align="center">
<a href="https://github.com/laravel/framework/actions"><img src="https://github.com/laravel/framework/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/dt/laravel/framework" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/v/laravel/framework" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/l/laravel/framework" alt="License"></a>
</p>

## About This Project

This is a Laravel-based application that serves as a backend API.

## Installation Guide

### Prerequisites

Make sure you have the following software installed on your machine:

*   PHP >= 8.2
*   Composer
*   Node.js & npm
*   A database server (e.g., MySQL, PostgreSQL)

### Step-by-Step Installation

1.  **Clone the repository:**

    ```bash
    git clone https://your-repository-url.git
    cd your-project-directory
    ```

2.  **Install PHP dependencies:**

    ```bash
    composer install
    ```

3.  **Install JavaScript dependencies:**

    ```bash
    npm install
    ```

4.  **Create your environment file:**

    Copy the example environment file and generate an application key.

    ```bash
    cp .env.example .env
    php artisan key:generate
    ```

5.  **Configure your environment variables in `.env`:**

    Open the `.env` file and update the following variables, especially the database credentials and external service keys:

    ```ini
    APP_NAME=OtoAPI
    APP_ENV=local
    APP_KEY= # This should be filled by the key:generate command
    APP_DEBUG=true
    APP_URL=http://localhost:8000

    DB_CONNECTION=mysql
    DB_HOST=127.0.0.1
    DB_PORT=3306
    DB_DATABASE=otoapi
    DB_USERNAME=root
    DB_PASSWORD=

    # Configure other services like Mailgun, Xendit, Jubelio, JNE
    # as needed based on the .env.example file.
    ```

6.  **Run database migrations and seeders:**

    This will create the necessary tables and populate them with initial data.

    ```bash
    php artisan migrate --seed
    ```

    You can also run individual seeders if needed. To run a specific seeder, use the `--class` option. It is important to run them in the following order due to dependencies between them:

    ```bash
    # 1. Seed Frontend Apps
    php artisan db:seed --class=FrontendAppSeeder

    # 2. Seed Membership Levels
    php artisan db:seed --class=MembershipLevelSeeder

    # 3. Seed Users
    php artisan db:seed --class=UserSeeder

    # 4. Seed Default Contact
    php artisan db:seed --class=ContactSeeder

    # 5. Seed Contact Supports
    php artisan db:seed --class=ContactSupportSeeder

    # 6. Seed JNE Defaults (requires JNE data to be imported first)
    php artisan db:seed --class=JneDefaultSeeder
    ```

7.  **Import JNE Data (Optional):**

    This command imports JNE branches, origins, and destinations from CSV files.

    ```bash
    php artisan otoapi:import-jne
    ```
    You can also specify the type to import:
    ```bash
    php artisan otoapi:import-jne branches
    php artisan otoapi:import-jne origins
    php artisan otoapi:import-jne destinations
    ```

8.  **Set up Xendit Webhook (Optional):**

    If you are using Xendit for payments, you need to set up the webhook.

    ```bash
    php artisan app:set-xendit-webhook
    ```

9.  **Run the development server:**

    You can use the built-in development script to start the server, queue worker, and log watcher.

    ```bash
    npm run dev
    ```

    Alternatively, you can run the PHP server manually:

    ```bash
    php artisan serve
    ```

    Your API should now be running at `http://localhost:8000`.

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
